<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تدفق الرسائل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .step.success { background: #d4edda; border-color: #c3e6cb; }
        .step.error { background: #f8d7da; border-color: #f5c6cb; }
        .step.active { background: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص تدفق الرسائل خطوة بخطوة</h1>
        
        <div class="step" id="step1">
            <h4>الخطوة 1: إرسال رسالة</h4>
            <button onclick="step1()">إرسال رسالة اختبار</button>
            <div id="result1"></div>
        </div>
        
        <div class="step" id="step2">
            <h4>الخطوة 2: فحص قاعدة البيانات مباشرة</h4>
            <button onclick="step2()">فحص آخر الرسائل</button>
            <div id="result2"></div>
        </div>
        
        <div class="step" id="step3">
            <h4>الخطوة 3: اختبار polling بأوقات مختلفة</h4>
            <button onclick="step3()">اختبار polling</button>
            <div id="result3"></div>
        </div>
        
        <div class="step" id="step4">
            <h4>الخطوة 4: فحص معرفات المستخدمين</h4>
            <button onclick="step4()">فحص المستخدمين</button>
            <div id="result4"></div>
        </div>
        
        <div id="log" class="log"></div>
        <button onclick="clearLog()">مسح السجل</button>
    </div>

    <script>
        const logElement = document.getElementById('log');
        let messageData = {};
        
        // المستخدمين للاختبار
        const sender = 'nurse_687b80778c2d8';
        const receiver = 'nurse_001';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry.trim());
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function updateStep(stepNum, status, message) {
            const step = document.getElementById(`step${stepNum}`);
            const result = document.getElementById(`result${stepNum}`);
            
            step.className = `step ${status}`;
            result.innerHTML = `<small>${message}</small>`;
        }
        
        // الخطوة 1: إرسال رسالة
        async function step1() {
            log('📤 الخطوة 1: إرسال رسالة اختبار...');
            updateStep(1, 'active', 'جاري الإرسال...');
            
            const content = `رسالة تشخيص - ${new Date().toLocaleTimeString()}`;
            
            try {
                const response = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: sender,
                        receiver_id: receiver,
                        content: content,
                        attachments: []
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    messageData = {
                        id: data.message_id,
                        timestamp: data.timestamp,
                        content: content,
                        sender: sender,
                        receiver: receiver
                    };
                    
                    log(`✅ تم إرسال الرسالة بنجاح`);
                    log(`📝 ID: ${data.message_id}`);
                    log(`⏰ الوقت: ${data.timestamp}`);
                    log(`👤 من: ${sender} إلى: ${receiver}`);
                    log(`💬 المحتوى: ${content}`);
                    
                    updateStep(1, 'success', `تم الإرسال - ID: ${data.message_id}`);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`❌ خطأ في الإرسال: ${error.message}`);
                updateStep(1, 'error', `خطأ: ${error.message}`);
            }
        }
        
        // الخطوة 2: فحص قاعدة البيانات
        async function step2() {
            log('🗄️ الخطوة 2: فحص قاعدة البيانات...');
            updateStep(2, 'active', 'جاري الفحص...');
            
            try {
                // استخدام polling للحصول على جميع الرسائل الحديثة
                const response = await fetch(`realtime-simple.php?action=poll&user_id=${receiver}&last_check=2024-01-01`);
                const data = await response.json();
                
                if (data.success) {
                    log(`📊 نتائج فحص قاعدة البيانات:`);
                    log(`  - رسائل جديدة للمستقبل: ${data.new_messages?.length || 0}`);
                    log(`  - جميع الرسائل الحديثة: ${data.debug?.all_recent_messages?.length || 0}`);
                    log(`  - رسائل المستخدم: ${data.debug?.all_messages_for_user?.length || 0}`);
                    
                    if (data.debug?.all_recent_messages) {
                        log(`📋 آخر الرسائل في قاعدة البيانات:`);
                        data.debug.all_recent_messages.forEach(msg => {
                            log(`  - ID: ${msg.id}, من: ${msg.sender_name} إلى: ${msg.receiver_name}, الوقت: ${msg.sent_at}`);
                        });
                    }
                    
                    if (data.debug?.all_messages_for_user) {
                        log(`📨 رسائل المستقبل (${receiver}):`);
                        data.debug.all_messages_for_user.forEach(msg => {
                            log(`  - ID: ${msg.id}, من: ${msg.sender_name}, الوقت: ${msg.sent_at}, النص: ${msg.message}`);
                        });
                    }
                    
                    // البحث عن رسالتنا
                    if (messageData.id) {
                        const foundInRecent = data.debug?.all_recent_messages?.find(msg => msg.id == messageData.id);
                        const foundForUser = data.debug?.all_messages_for_user?.find(msg => msg.id == messageData.id);
                        
                        if (foundInRecent) {
                            log(`✅ تم العثور على الرسالة في قاعدة البيانات`);
                            updateStep(2, 'success', 'الرسالة موجودة في قاعدة البيانات');
                        } else {
                            log(`❌ لم يتم العثور على الرسالة في قاعدة البيانات`);
                            updateStep(2, 'error', 'الرسالة غير موجودة في قاعدة البيانات');
                        }
                        
                        if (foundForUser) {
                            log(`✅ الرسالة مرتبطة بالمستقبل الصحيح`);
                        } else {
                            log(`❌ الرسالة غير مرتبطة بالمستقبل`);
                        }
                    }
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`❌ خطأ في فحص قاعدة البيانات: ${error.message}`);
                updateStep(2, 'error', `خطأ: ${error.message}`);
            }
        }
        
        // الخطوة 3: اختبار polling
        async function step3() {
            log('🔄 الخطوة 3: اختبار polling بأوقات مختلفة...');
            updateStep(3, 'active', 'جاري الاختبار...');
            
            if (!messageData.timestamp) {
                log('❌ لا توجد رسالة للاختبار - يرجى تشغيل الخطوة 1 أولاً');
                updateStep(3, 'error', 'لا توجد رسالة للاختبار');
                return;
            }
            
            try {
                // اختبار 1: polling بوقت قديم جداً
                log('🔍 اختبار 1: polling بوقت قديم...');
                let response = await fetch(`realtime-simple.php?action=poll&user_id=${receiver}&last_check=2024-01-01`);
                let data = await response.json();
                
                if (data.success) {
                    log(`  - رسائل جديدة: ${data.new_messages?.length || 0}`);
                    const found1 = data.new_messages?.find(msg => msg.id == messageData.id);
                    log(`  - رسالتنا موجودة: ${found1 ? 'نعم' : 'لا'}`);
                }
                
                // اختبار 2: polling بوقت الرسالة بالضبط
                log('🔍 اختبار 2: polling بوقت الرسالة...');
                response = await fetch(`realtime-simple.php?action=poll&user_id=${receiver}&last_check=${encodeURIComponent(messageData.timestamp)}`);
                data = await response.json();
                
                if (data.success) {
                    log(`  - رسائل جديدة: ${data.new_messages?.length || 0}`);
                    const found2 = data.new_messages?.find(msg => msg.id == messageData.id);
                    log(`  - رسالتنا موجودة: ${found2 ? 'نعم' : 'لا'}`);
                    
                    if (data.debug) {
                        log(`🔍 معلومات التشخيص:`);
                        log(`  - المستخدم: ${data.debug.user_id}`);
                        log(`  - آخر فحص: ${data.debug.last_check}`);
                        log(`  - الوقت الحالي: ${data.debug.current_time}`);
                        log(`  - الاستعلام: ${data.debug.query_executed}`);
                    }
                }
                
                // اختبار 3: polling بوقت قبل الرسالة بدقيقة
                const beforeTime = new Date(new Date(messageData.timestamp).getTime() - 60000);
                const beforeTimeStr = beforeTime.getFullYear() + '-' +
                    String(beforeTime.getMonth() + 1).padStart(2, '0') + '-' +
                    String(beforeTime.getDate()).padStart(2, '0') + ' ' +
                    String(beforeTime.getHours()).padStart(2, '0') + ':' +
                    String(beforeTime.getMinutes()).padStart(2, '0') + ':' +
                    String(beforeTime.getSeconds()).padStart(2, '0');
                
                log('🔍 اختبار 3: polling بوقت قبل الرسالة...');
                response = await fetch(`realtime-simple.php?action=poll&user_id=${receiver}&last_check=${encodeURIComponent(beforeTimeStr)}`);
                data = await response.json();
                
                if (data.success) {
                    log(`  - رسائل جديدة: ${data.new_messages?.length || 0}`);
                    const found3 = data.new_messages?.find(msg => msg.id == messageData.id);
                    log(`  - رسالتنا موجودة: ${found3 ? 'نعم' : 'لا'}`);
                    
                    if (found3) {
                        log(`✅ نجح! الرسالة تظهر عند polling بوقت قبل الإرسال`);
                        updateStep(3, 'success', 'polling يعمل بشكل صحيح');
                    } else {
                        log(`❌ فشل! الرسالة لا تظهر حتى مع وقت قديم`);
                        updateStep(3, 'error', 'مشكلة في polling');
                    }
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار polling: ${error.message}`);
                updateStep(3, 'error', `خطأ: ${error.message}`);
            }
        }
        
        // الخطوة 4: فحص المستخدمين
        async function step4() {
            log('👥 الخطوة 4: فحص معرفات المستخدمين...');
            updateStep(4, 'active', 'جاري الفحص...');
            
            try {
                const response = await fetch('realtime-simple.php?action=poll&user_id=test&last_check=2024-01-01');
                const data = await response.json();
                
                if (data.success && data.online_users) {
                    log(`👥 المستخدمين المتاحين:`);
                    data.online_users.forEach(user => {
                        log(`  - ID: ${user.id}, الاسم: ${user.name}, الدور: ${user.role}`);
                    });
                    
                    const senderExists = data.online_users.find(u => u.id === sender);
                    const receiverExists = data.online_users.find(u => u.id === receiver);
                    
                    log(`🔍 فحص المعرفات:`);
                    log(`  - المرسل (${sender}): ${senderExists ? 'موجود' : 'غير موجود'}`);
                    log(`  - المستقبل (${receiver}): ${receiverExists ? 'موجود' : 'غير موجود'}`);
                    
                    if (senderExists && receiverExists) {
                        updateStep(4, 'success', 'جميع المعرفات صحيحة');
                    } else {
                        updateStep(4, 'error', 'بعض المعرفات غير صحيحة');
                    }
                } else {
                    throw new Error('فشل في جلب قائمة المستخدمين');
                }
            } catch (error) {
                log(`❌ خطأ في فحص المستخدمين: ${error.message}`);
                updateStep(4, 'error', `خطأ: ${error.message}`);
            }
        }
        
        // تشغيل تلقائي
        window.addEventListener('load', () => {
            log('🚀 بدء تشخيص تدفق الرسائل');
            log(`👤 المرسل: ${sender}`);
            log(`👤 المستقبل: ${receiver}`);
        });
    </script>
</body>
</html>
