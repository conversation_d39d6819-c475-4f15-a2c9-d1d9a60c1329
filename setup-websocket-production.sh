#!/bin/bash

echo "🚀 إعداد خادم WebSocket للموقع المباشر"
echo "=========================================="

# التحقق من الصلاحيات
if [ "$EUID" -ne 0 ]; then
    echo "❌ يرجى تشغيل هذا السكريبت بصلاحيات root"
    echo "استخدم: sudo ./setup-websocket-production.sh"
    exit 1
fi

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "❌ PHP غير مثبت"
    echo "تثبيت PHP..."
    apt update
    apt install -y php php-cli php-mysql php-mbstring php-xml
fi

# التحقق من وجود Composer
if ! command -v composer &> /dev/null; then
    echo "❌ Composer غير مثبت"
    echo "تثبيت Composer..."
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
fi

# تثبيت المكتبات
echo "📦 تثبيت مكتبات WebSocket..."
composer install --no-dev --optimize-autoloader

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المكتبات"
    exit 1
fi

# إنشاء مجلد السجلات
mkdir -p /var/log/websocket
chown www-data:www-data /var/log/websocket

# إنشاء ملف خدمة systemd
echo "⚙️ إنشاء خدمة النظام..."
cat > /etc/systemd/system/websocket-csmanager.service << EOF
[Unit]
Description=WebSocket Server for CS Manager
After=network.target mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/php $(pwd)/websocket-production.php
Restart=always
RestartSec=10
StandardOutput=append:/var/log/websocket/output.log
StandardError=append:/var/log/websocket/error.log

# حدود الأمان
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/websocket

[Install]
WantedBy=multi-user.target
EOF

# إعادة تحميل systemd
systemctl daemon-reload

# فتح المنفذ في الجدار الناري
echo "🔥 فتح المنفذ 8080 في الجدار الناري..."
if command -v ufw &> /dev/null; then
    ufw allow 8080/tcp
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=8080/tcp
    firewall-cmd --reload
fi

# تحديث إعدادات قاعدة البيانات
echo "🗄️ تحديث إعدادات قاعدة البيانات..."
echo "يرجى تحديث الإعدادات في ملف websocket-production.php:"
echo "- اسم قاعدة البيانات"
echo "- اسم المستخدم"
echo "- كلمة المرور"
echo ""

# إنشاء سكريبت بدء التشغيل
cat > start-websocket-production.sh << 'EOF'
#!/bin/bash
echo "🚀 بدء تشغيل خادم WebSocket للإنتاج..."

# التحقق من إعدادات قاعدة البيانات
php -r "
\$config = file_get_contents('websocket-production.php');
if (strpos(\$config, 'your_production_password') !== false) {
    echo \"❌ يرجى تحديث إعدادات قاعدة البيانات في websocket-production.php\n\";
    exit(1);
}
echo \"✅ إعدادات قاعدة البيانات محدثة\n\";
"

if [ $? -ne 0 ]; then
    exit 1
fi

# بدء الخدمة
sudo systemctl start websocket-csmanager
sudo systemctl enable websocket-csmanager

echo "✅ تم بدء تشغيل خادم WebSocket"
echo "📊 لمراقبة الحالة: sudo systemctl status websocket-csmanager"
echo "📋 لعرض السجلات: sudo journalctl -u websocket-csmanager -f"
EOF

chmod +x start-websocket-production.sh

# إنشاء سكريبت إيقاف التشغيل
cat > stop-websocket-production.sh << 'EOF'
#!/bin/bash
echo "🛑 إيقاف خادم WebSocket..."
sudo systemctl stop websocket-csmanager
sudo systemctl disable websocket-csmanager
echo "✅ تم إيقاف خادم WebSocket"
EOF

chmod +x stop-websocket-production.sh

# إنشاء سكريبت مراقبة
cat > monitor-websocket.sh << 'EOF'
#!/bin/bash
echo "📊 مراقبة خادم WebSocket"
echo "========================="

# حالة الخدمة
echo "🔍 حالة الخدمة:"
systemctl status websocket-csmanager --no-pager

echo ""
echo "📈 استخدام الموارد:"
ps aux | grep websocket-production | grep -v grep

echo ""
echo "🌐 الاتصالات النشطة:"
netstat -an | grep :8080 | grep ESTABLISHED | wc -l

echo ""
echo "📋 آخر 10 سجلات:"
tail -10 /var/log/websocket/output.log

echo ""
echo "❌ آخر أخطاء:"
tail -5 /var/log/websocket/error.log
EOF

chmod +x monitor-websocket.sh

echo ""
echo "✅ تم إعداد خادم WebSocket بنجاح!"
echo ""
echo "📋 الخطوات التالية:"
echo "1. حدث إعدادات قاعدة البيانات في websocket-production.php"
echo "2. شغل الخادم: ./start-websocket-production.sh"
echo "3. راقب الحالة: ./monitor-websocket.sh"
echo ""
echo "🔧 أوامر مفيدة:"
echo "- بدء التشغيل: sudo systemctl start websocket-csmanager"
echo "- إيقاف التشغيل: sudo systemctl stop websocket-csmanager"
echo "- إعادة التشغيل: sudo systemctl restart websocket-csmanager"
echo "- مراقبة السجلات: sudo journalctl -u websocket-csmanager -f"
echo ""
echo "🌐 عنوان WebSocket: wss://www.csmanager.online:8080"
