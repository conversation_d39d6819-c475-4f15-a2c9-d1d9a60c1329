<?php
/**
 * WebSocket Fallback - HTTP Polling للرسائل الفورية
 * يعمل كبديل لـ WebSocket عندما لا يكون متاحاً
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'j5aKN6lz5bsujTcWaYAd';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$userId = $_GET['user_id'] ?? $_POST['user_id'] ?? '';

switch ($action) {
    case 'poll':
        handlePoll($pdo, $userId);
        break;
        
    case 'send':
        handleSend($pdo);
        break;
        
    case 'typing':
        handleTyping($pdo);
        break;
        
    case 'heartbeat':
        handleHeartbeat($pdo, $userId);
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
}

function handlePoll($pdo, $userId) {
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => 'User ID required']);
        return;
    }
    
    $lastCheck = $_GET['last_check'] ?? date('Y-m-d H:i:s', time() - 30);
    
    try {
        // فحص الرسائل الجديدة
        $stmt = $pdo->prepare("
            SELECT m.*, u.name as sender_name 
            FROM messages m 
            LEFT JOIN users u ON m.sender_id = u.id 
            WHERE m.receiver_id = ? AND m.created_at > ? 
            ORDER BY m.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId, $lastCheck]);
        $newMessages = $stmt->fetchAll();
        
        // فحص المستخدمين المتصلين (محاكاة)
        $stmt = $pdo->prepare("
            SELECT DISTINCT u.id, u.name, 'online' as status
            FROM users u 
            WHERE u.last_activity > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            AND u.id != ?
        ");
        $stmt->execute([$userId]);
        $onlineUsers = $stmt->fetchAll();
        
        // تحديث آخر نشاط للمستخدم الحالي
        $stmt = $pdo->prepare("UPDATE users SET last_activity = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
        
        echo json_encode([
            'success' => true,
            'new_messages' => $newMessages,
            'online_users' => $onlineUsers,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Poll failed: ' . $e->getMessage()]);
    }
}

function handleSend($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $senderId = $input['sender_id'] ?? '';
    $receiverId = $input['receiver_id'] ?? '';
    $content = $input['content'] ?? '';
    $attachments = $input['attachments'] ?? [];
    
    if (!$senderId || !$receiverId || !$content) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO messages (sender_id, receiver_id, message, attachments, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $attachmentsJson = empty($attachments) ? null : json_encode($attachments);
        $stmt->execute([$senderId, $receiverId, $content, $attachmentsJson]);
        
        $messageId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message_id' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Send failed: ' . $e->getMessage()]);
    }
}

function handleTyping($pdo) {
    // محاكاة مؤشر الكتابة (يمكن تحسينها بجدول منفصل)
    $input = json_decode(file_get_contents('php://input'), true);
    
    echo json_encode([
        'success' => true,
        'message' => 'Typing indicator received'
    ]);
}

function handleHeartbeat($pdo, $userId) {
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => 'User ID required']);
        return;
    }
    
    try {
        // تحديث آخر نشاط
        $stmt = $pdo->prepare("UPDATE users SET last_activity = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
        
        echo json_encode([
            'success' => true,
            'timestamp' => time()
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Heartbeat failed: ' . $e->getMessage()]);
    }
}
?>
