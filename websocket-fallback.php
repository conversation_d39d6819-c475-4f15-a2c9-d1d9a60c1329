<?php
/**
 * WebSocket Fallback - HTTP Polling للرسائل الفورية
 * يعمل كبديل لـ WebSocket عندما لا يكون متاحاً
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'j5aKN6lz5bsujTcWaYAd';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// تسجيل الطلب للتطوير
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestData = $requestMethod === 'POST' ? file_get_contents('php://input') : '';
error_log("WebSocket Fallback Request: Method=$requestMethod, GET=" . json_encode($_GET) . ", POST_DATA=$requestData");

$action = $_GET['action'] ?? $_POST['action'] ?? '';

// للطلبات POST، جرب قراءة action من JSON body
if (empty($action) && $requestMethod === 'POST') {
    $postData = json_decode($requestData, true);
    $action = $postData['action'] ?? '';
}

$userId = $_GET['user_id'] ?? $_POST['user_id'] ?? '';

error_log("Parsed action: $action, userId: $userId");

switch ($action) {
    case 'poll':
        handlePoll($pdo, $userId);
        break;

    case 'send':
        handleSend($pdo);
        break;

    case 'typing':
        handleTyping($pdo);
        break;

    case 'heartbeat':
        handleHeartbeat($pdo, $userId);
        break;

    default:
        http_response_code(400);
        echo json_encode([
            'error' => 'Invalid action',
            'received_action' => $action,
            'method' => $requestMethod,
            'available_actions' => ['poll', 'send', 'typing', 'heartbeat']
        ]);
}

function handlePoll($pdo, $userId) {
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => 'User ID required']);
        return;
    }

    $lastCheck = $_GET['last_check'] ?? date('Y-m-d H:i:s', time() - 30);

    // تسجيل للتطوير
    error_log("Polling request: user_id=$userId, last_check=$lastCheck");
    
    try {
        // فحص الرسائل الجديدة
        $stmt = $pdo->prepare("
            SELECT m.*, u.name as sender_name
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            WHERE m.receiver_id = ? AND m.sent_at > ?
            ORDER BY m.sent_at DESC
            LIMIT 10
        ");
        $stmt->execute([$userId, $lastCheck]);
        $newMessages = $stmt->fetchAll();
        
        // فحص المستخدمين المتصلين (محاكاة مبسطة)
        $onlineUsers = [];

        try {
            // محاولة الحصول على المستخدمين النشطين
            $stmt = $pdo->prepare("
                SELECT DISTINCT u.id, u.name, 'online' as status
                FROM users u
                WHERE u.is_active = 1
                AND u.id != ?
                LIMIT 10
            ");
            $stmt->execute([$userId]);
            $onlineUsers = $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting online users: " . $e->getMessage());
            // في حالة الخطأ، أرجع قائمة فارغة
            $onlineUsers = [];
        }

        // إضافة المستخدم الحالي كمتصل (لأنه يقوم بـ polling)
        try {
            $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $currentUserData = $stmt->fetch();
            if ($currentUserData) {
                $onlineUsers[] = [
                    'id' => $currentUserData['id'],
                    'name' => $currentUserData['name'],
                    'status' => 'online'
                ];
            }
        } catch (Exception $e) {
            error_log("Error getting current user: " . $e->getMessage());
        }

        $response = [
            'success' => true,
            'new_messages' => $newMessages,
            'online_users' => $onlineUsers,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // تسجيل للتطوير
        error_log("Polling response: " . json_encode([
            'user_id' => $userId,
            'new_messages_count' => count($newMessages),
            'online_users_count' => count($onlineUsers)
        ]));

        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Poll failed: ' . $e->getMessage()]);
    }
}

function handleSend($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $senderId = $input['sender_id'] ?? '';
    $receiverId = $input['receiver_id'] ?? '';
    $content = $input['content'] ?? '';
    $attachments = $input['attachments'] ?? [];
    
    if (!$senderId || !$receiverId || !$content) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO messages (sender_id, receiver_id, message, attachments)
            VALUES (?, ?, ?, ?)
        ");
        
        $attachmentsJson = empty($attachments) ? null : json_encode($attachments);
        $stmt->execute([$senderId, $receiverId, $content, $attachmentsJson]);
        
        $messageId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message_id' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Send failed: ' . $e->getMessage()]);
    }
}

function handleTyping($pdo) {
    // محاكاة مؤشر الكتابة (يمكن تحسينها بجدول منفصل)
    $input = json_decode(file_get_contents('php://input'), true);
    
    echo json_encode([
        'success' => true,
        'message' => 'Typing indicator received'
    ]);
}

function handleHeartbeat($pdo, $userId) {
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => 'User ID required']);
        return;
    }

    try {
        // التحقق من وجود المستخدم (بدلاً من تحديث last_activity)
        $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            http_response_code(404);
            echo json_encode(['error' => 'User not found or inactive']);
            return;
        }

        echo json_encode([
            'success' => true,
            'timestamp' => time(),
            'user' => $user
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Heartbeat failed: ' . $e->getMessage()]);
    }
}
?>
