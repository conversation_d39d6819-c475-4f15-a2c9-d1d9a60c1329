# 🚀 تشغيل سريع للرسائل الفورية

## ⚡ الطريقة السريعة

### 1. تحميل المكتبات المطلوبة
```bash
# تحميل Composer إذا لم يكن مثبت
# من: https://getcomposer.org/download/

# تثبيت المكتبات
composer install
```

### 2. تشغيل خادم WebSocket
```bash
# الطريقة الأولى - استخدام الملف المُعد مسبقاً
start-websocket.bat

# الطريقة الثانية - تشغيل مباشر
php websocket-server.php
```

### 3. فتح التطبيق
- افتح المتصفح واذهب إلى: `http://localhost/color/cs-manager.html`
- ادخل إلى صفحة الرسائل
- يجب أن ترى "🟢 رسائل فورية" في أعلى الشاشة

## 🔧 حل المشاكل الشائعة

### المشكلة: "WebSocket connection failed"
**السبب**: خادم WebSocket غير مُشغل

**الحل**:
1. افتح Command Prompt جديد
2. انتقل لمجلد المشروع: `cd C:\Users\<USER>\Desktop\color`
3. شغل الخادم: `php websocket-server.php`

### المشكلة: "composer: command not found"
**الحل**: 
1. حمل Composer من: https://getcomposer.org/download/
2. ثبته واعد تشغيل Command Prompt

### المشكلة: "php: command not found"
**الحل**:
1. تأكد من تثبيت PHP
2. أضف PHP إلى PATH في Windows

## 📱 الاستخدام بدون WebSocket

إذا لم تتمكن من تشغيل خادم WebSocket:
- النظام سيعمل بالطريقة التقليدية تلقائياً
- ستظهر رسالة "🔴 رسائل تقليدية"
- الرسائل ستعمل لكن بدون الميزات الفورية
- يمكنك النقر على "إعادة الاتصال" لاحقاً

## ✅ علامات النجاح

عند تشغيل الخادم بنجاح ستظهر:
```
🚀 خادم WebSocket يعمل على المنفذ 8080
📡 في انتظار الاتصالات...
```

في التطبيق ستظهر:
- "🟢 رسائل فورية" في أعلى الشاشة
- في Console: "🔗 تم الاتصال بخادم WebSocket"

## 🎯 الميزات المتاحة

### مع WebSocket (الوضع الفوري):
- ✅ رسائل فورية بدون إعادة تحميل
- ✅ حالة الاتصال (متصل/غير متصل)
- ✅ مؤشر "يكتب..."
- ✅ إشعارات صوتية
- ✅ إشعارات فورية

### بدون WebSocket (الوضع التقليدي):
- ✅ إرسال واستقبال الرسائل
- ❌ تحديث فوري (يحتاج إعادة تحميل)
- ❌ حالة الاتصال
- ❌ مؤشر الكتابة

## 🔄 إعادة التشغيل

إذا توقف الخادم:
1. أغلق Command Prompt
2. افتح Command Prompt جديد
3. انتقل للمجلد: `cd C:\Users\<USER>\Desktop\color`
4. شغل الخادم: `php websocket-server.php`
5. في التطبيق، انقر "إعادة الاتصال"
