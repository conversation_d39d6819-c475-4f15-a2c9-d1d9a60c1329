<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراكز الصحية - إصدار مبسط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .status-indicator.connecting {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .status-indicator.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 نظام إدارة المراكز الصحية - إصدار مبسط</h1>
        
        <div id="statusIndicator" class="status-indicator connecting">
            🔄 جاري التهيئة...
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبار النظام المبسط</h3>
            <button onclick="testSystem()">اختبار النظام</button>
            <button onclick="testAPI()">اختبار API</button>
            <button onclick="initRealtime()">تهيئة الرسائل الفورية</button>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل النظام</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="simple-realtime.js"></script>
    <script>
        // متغيرات النظام
        let currentUser = {
            id: 'nurse_687b80778c2d8',
            name: 'مستخدم تجريبي',
            role: 'nurse'
        };
        
        let realtimeSystem = null;
        const logElement = document.getElementById('log');
        
        // دالة تسجيل الرسائل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
        }
        
        // تحديث مؤشر الحالة
        function updateStatus(status, message) {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${status}`;
            indicator.textContent = message;
        }
        
        // اختبار النظام الأساسي
        function testSystem() {
            log('🧪 بدء اختبار النظام الأساسي...');
            
            try {
                // اختبار JavaScript الأساسي
                const testObj = { name: 'test', value: 123 };
                const testArray = [1, 2, 3];
                const testFunction = () => 'works';
                
                log('✅ JavaScript الأساسي يعمل');
                log('✅ الكائنات والمصفوفات تعمل');
                log('✅ الدوال تعمل: ' + testFunction());
                
                // اختبار fetch API
                if (typeof fetch !== 'undefined') {
                    log('✅ Fetch API متاح');
                } else {
                    log('❌ Fetch API غير متاح');
                }
                
                // اختبار SimpleRealtime
                if (typeof SimpleRealtime !== 'undefined') {
                    log('✅ SimpleRealtime متاح');
                    const testRealtime = new SimpleRealtime();
                    log('✅ تم إنشاء كائن SimpleRealtime بنجاح');
                } else {
                    log('❌ SimpleRealtime غير متاح');
                }
                
                updateStatus('connected', '✅ النظام يعمل');
                log('🎉 جميع الاختبارات نجحت!');
                
            } catch (error) {
                log('❌ خطأ في اختبار النظام: ' + error.message);
                updateStatus('error', '❌ خطأ في النظام');
            }
        }
        
        // اختبار API
        async function testAPI() {
            log('🔌 اختبار API...');
            
            try {
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API يعمل بنجاح');
                    log(`📊 المستخدمين: ${data.total_users}, الرسائل: ${data.recent_messages_24h}`);
                    updateStatus('connected', '✅ API متاح');
                } else {
                    log('❌ API لا يعمل: ' + JSON.stringify(data));
                    updateStatus('error', '❌ API لا يعمل');
                }
            } catch (error) {
                log('❌ خطأ في API: ' + error.message);
                updateStatus('error', '❌ خطأ في الاتصال');
            }
        }
        
        // تهيئة النظام الفوري
        async function initRealtime() {
            log('🚀 تهيئة نظام الرسائل الفورية...');
            
            try {
                if (typeof SimpleRealtime === 'undefined') {
                    throw new Error('SimpleRealtime غير متاح');
                }
                
                realtimeSystem = new SimpleRealtime();
                
                // إعداد معالجات الأحداث
                realtimeSystem.onMessage((message) => {
                    log(`📨 رسالة جديدة من ${message.sender_name}: ${message.content}`);
                });
                
                realtimeSystem.onStatusChange((event) => {
                    if (event.type === 'status_change') {
                        log(`🔄 تغيير الحالة: ${event.status}`);
                        updateStatus('connected', '🟢 رسائل فورية');
                    } else if (Array.isArray(event)) {
                        log(`👥 مستخدمين متصلين: ${event.length}`);
                    }
                });
                
                // تهيئة النظام
                const success = await realtimeSystem.init(currentUser);
                
                if (success) {
                    log('✅ تم تهيئة النظام الفوري بنجاح');
                    updateStatus('connected', '🟢 رسائل فورية');
                    
                    // عرض معلومات النظام
                    const status = realtimeSystem.getStatus();
                    log(`📊 حالة النظام: نشط=${status.active}, مستخدمين=${status.online_users_count}`);
                } else {
                    throw new Error('فشل في تهيئة النظام');
                }
                
            } catch (error) {
                log('❌ خطأ في تهيئة النظام الفوري: ' + error.message);
                updateStatus('error', '❌ خطأ في التهيئة');
            }
        }
        
        // تهيئة تلقائية عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 بدء تحميل النظام...');
            
            setTimeout(() => {
                testSystem();
            }, 1000);
            
            setTimeout(() => {
                testAPI();
            }, 2000);
            
            setTimeout(() => {
                initRealtime();
            }, 3000);
        });
        
        // معالجة الأخطاء العامة
        window.addEventListener('error', (event) => {
            log('❌ خطأ JavaScript: ' + event.message);
            log('📍 الملف: ' + event.filename + ':' + event.lineno);
            updateStatus('error', '❌ خطأ في JavaScript');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log('❌ خطأ Promise: ' + event.reason);
            updateStatus('error', '❌ خطأ في Promise');
        });
    </script>
</body>
</html>
