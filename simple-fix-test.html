<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            width: 100%;
        }
        button:hover { background: #0056b3; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .result.success { background: #d4edda; color: #155724; }
        .result.error { background: #f8d7da; color: #721c24; }
        .result.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح الرسائل الفورية</h1>
        
        <button onclick="runCompleteTest()">🧪 تشغيل اختبار شامل</button>
        
        <div id="result" class="result info">اضغط الزر لبدء الاختبار</div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        const resultElement = document.getElementById('result');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry.trim());
        }
        
        function updateResult(status, message) {
            resultElement.className = `result ${status}`;
            resultElement.textContent = message;
        }
        
        async function runCompleteTest() {
            log('🚀 بدء الاختبار الشامل للرسائل الفورية...');
            updateResult('info', 'جاري التشغيل...');
            
            try {
                // الخطوة 1: الحصول على مستخدمين حقيقيين
                log('👥 الخطوة 1: جلب المستخدمين المتاحين...');
                const usersResponse = await fetch('realtime-simple.php?action=poll&user_id=test&last_check=2024-01-01');
                const usersData = await usersResponse.json();
                
                if (!usersData.success || !usersData.online_users || usersData.online_users.length < 2) {
                    throw new Error('لا يوجد مستخدمين كافيين في قاعدة البيانات');
                }
                
                const sender = usersData.online_users[0];
                const receiver = usersData.online_users[1];
                
                log(`✅ تم العثور على ${usersData.online_users.length} مستخدم`);
                log(`👤 المرسل: ${sender.name} (${sender.id})`);
                log(`👤 المستقبل: ${receiver.name} (${receiver.id})`);
                
                // الخطوة 2: إرسال رسالة
                log('📤 الخطوة 2: إرسال رسالة اختبار...');
                const content = `رسالة اختبار إصلاح - ${new Date().toLocaleTimeString()}`;
                
                const sendResponse = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: sender.id,
                        receiver_id: receiver.id,
                        content: content,
                        attachments: []
                    })
                });
                
                const sendData = await sendResponse.json();
                
                if (!sendData.success) {
                    throw new Error(`فشل في الإرسال: ${sendData.error}`);
                }
                
                log(`✅ تم إرسال الرسالة بنجاح - ID: ${sendData.message_id}`);
                log(`⏰ وقت الإرسال: ${sendData.timestamp}`);
                
                // الخطوة 3: انتظار قليل ثم فحص الاستقبال
                log('⏳ الخطوة 3: انتظار 3 ثوان ثم فحص الاستقبال...');
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // استخدام وقت قبل الإرسال بدقيقة للتأكد
                const beforeSendTime = new Date(new Date(sendData.timestamp).getTime() - 60000);
                const beforeSendTimeStr = beforeSendTime.getFullYear() + '-' +
                    String(beforeSendTime.getMonth() + 1).padStart(2, '0') + '-' +
                    String(beforeSendTime.getDate()).padStart(2, '0') + ' ' +
                    String(beforeSendTime.getHours()).padStart(2, '0') + ':' +
                    String(beforeSendTime.getMinutes()).padStart(2, '0') + ':' +
                    String(beforeSendTime.getSeconds()).padStart(2, '0');
                
                log(`🔍 فحص الرسائل منذ: ${beforeSendTimeStr}`);
                
                const pollResponse = await fetch(`realtime-simple.php?action=poll&user_id=${receiver.id}&last_check=${encodeURIComponent(beforeSendTimeStr)}`);
                const pollData = await pollResponse.json();
                
                if (!pollData.success) {
                    throw new Error(`فشل في polling: ${pollData.error}`);
                }
                
                log(`📊 نتائج polling:`);
                log(`  - رسائل جديدة (>=): ${pollData.new_messages?.length || 0}`);
                log(`  - رسائل جديدة (>): ${pollData.debug?.received_count_alt || 0}`);
                log(`  - جميع رسائل المستخدم: ${pollData.debug?.all_messages_for_user?.length || 0}`);
                
                // البحث عن رسالتنا
                const ourMessage = pollData.new_messages?.find(msg => msg.id == sendData.message_id);
                const ourMessageAlt = pollData.debug?.new_messages_alt?.find(msg => msg.id == sendData.message_id);
                
                if (ourMessage || ourMessageAlt) {
                    log('🎉 نجح! تم العثور على الرسالة');
                    log(`📨 الرسالة: ${(ourMessage || ourMessageAlt).message}`);
                    updateResult('success', '✅ الرسائل الفورية تعمل بنجاح!');
                    
                    // الخطوة 4: اختبار النظام الفوري الكامل
                    log('🔄 الخطوة 4: اختبار النظام الفوري الكامل...');
                    await testRealtimeSystem(sender, receiver);
                    
                } else {
                    log('❌ فشل! لم يتم العثور على الرسالة');
                    
                    // عرض معلومات التشخيص
                    if (pollData.debug) {
                        log('🔍 معلومات التشخيص:');
                        log(`  - المستخدم: ${pollData.debug.user_id}`);
                        log(`  - آخر فحص: ${pollData.debug.last_check}`);
                        log(`  - الوقت الحالي: ${pollData.debug.current_time}`);
                        log(`  - الاستعلام (>=): ${pollData.debug.query_executed_gte}`);
                        log(`  - الاستعلام (>): ${pollData.debug.query_executed_gt}`);
                        
                        if (pollData.debug.all_messages_for_user) {
                            log('📋 جميع رسائل المستخدم:');
                            pollData.debug.all_messages_for_user.forEach(msg => {
                                log(`  - ID: ${msg.id}, الوقت: ${msg.sent_at}, النص: ${msg.message}`);
                            });
                        }
                    }
                    
                    updateResult('error', '❌ الرسائل الفورية لا تعمل - راجع السجل');
                }
                
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`);
                updateResult('error', `❌ خطأ: ${error.message}`);
            }
        }
        
        async function testRealtimeSystem(sender, receiver) {
            try {
                log('🔄 اختبار النظام الفوري الكامل...');
                
                // محاكاة النظام الفوري
                let messageReceived = false;
                let pollCount = 0;
                
                const pollInterval = setInterval(async () => {
                    pollCount++;
                    log(`🔄 Poll #${pollCount}...`);
                    
                    try {
                        const lastCheck = new Date(Date.now() - 10000);
                        const lastCheckStr = lastCheck.getFullYear() + '-' +
                            String(lastCheck.getMonth() + 1).padStart(2, '0') + '-' +
                            String(lastCheck.getDate()).padStart(2, '0') + ' ' +
                            String(lastCheck.getHours()).padStart(2, '0') + ':' +
                            String(lastCheck.getMinutes()).padStart(2, '0') + ':' +
                            String(lastCheck.getSeconds()).padStart(2, '0');
                        
                        const response = await fetch(`realtime-simple.php?action=poll&user_id=${receiver.id}&last_check=${encodeURIComponent(lastCheckStr)}`);
                        const data = await response.json();
                        
                        if (data.success && data.new_messages && data.new_messages.length > 0) {
                            log(`📬 Poll #${pollCount}: ${data.new_messages.length} رسائل جديدة`);
                            messageReceived = true;
                        } else {
                            log(`📭 Poll #${pollCount}: لا توجد رسائل جديدة`);
                        }
                        
                        if (pollCount >= 3) {
                            clearInterval(pollInterval);
                            
                            if (messageReceived) {
                                log('🎉 النظام الفوري يعمل! تم استقبال رسائل أثناء polling');
                                updateResult('success', '🎉 النظام الفوري يعمل بنجاح!');
                            } else {
                                log('✅ النظام الفوري جاهز (لا توجد رسائل جديدة أثناء الاختبار)');
                                updateResult('success', '✅ النظام الفوري جاهز للاستخدام');
                            }
                        }
                    } catch (error) {
                        log(`❌ خطأ في Poll #${pollCount}: ${error.message}`);
                    }
                }, 3000);
                
                // إرسال رسالة أثناء polling
                setTimeout(async () => {
                    try {
                        log('📤 إرسال رسالة أثناء polling...');
                        const testContent = `رسالة أثناء polling - ${new Date().toLocaleTimeString()}`;
                        
                        const response = await fetch('realtime-simple.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                action: 'send',
                                sender_id: sender.id,
                                receiver_id: receiver.id,
                                content: testContent,
                                attachments: []
                            })
                        });
                        
                        const data = await response.json();
                        if (data.success) {
                            log(`✅ تم إرسال رسالة polling - ID: ${data.message_id}`);
                        }
                    } catch (error) {
                        log(`❌ خطأ في إرسال رسالة polling: ${error.message}`);
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ خطأ في اختبار النظام الفوري: ${error.message}`);
            }
        }
        
        // تشغيل تلقائي
        window.addEventListener('load', () => {
            log('🚀 مرحباً بك في اختبار إصلاح الرسائل الفورية');
            log('💡 اضغط الزر لبدء الاختبار الشامل');
        });
    </script>
</body>
</html>
