/**
 * نظام التحقق من الرسائل الفورية
 * يمكن تشغيله في console المتصفح للتحقق من عمل النظام
 */

// دالة التحقق الشاملة
async function verifyRealtimeSystem() {
    console.log('🔍 بدء التحقق من نظام الرسائل الفورية...');
    console.log('='.repeat(50));
    
    const results = {
        websocket: false,
        polling: false,
        api: false,
        user: false,
        overall: false
    };
    
    // 1. فحص المستخدم الحالي
    console.log('👤 فحص المستخدم الحالي...');
    if (typeof currentUser !== 'undefined' && currentUser && currentUser.id) {
        console.log('✅ المستخدم متصل:', currentUser.name, '(ID:', currentUser.id + ')');
        results.user = true;
    } else {
        console.log('❌ لا يوجد مستخدم متصل');
        return results;
    }
    
    // 2. فحص حالة WebSocket
    console.log('\n🌐 فحص حالة WebSocket...');
    if (typeof websocket !== 'undefined' && websocket && websocket.readyState === WebSocket.OPEN) {
        console.log('✅ WebSocket متصل ويعمل');
        results.websocket = true;
    } else {
        console.log('❌ WebSocket غير متصل أو لا يعمل');
        console.log('   - حالة WebSocket:', typeof websocket !== 'undefined' ? websocket?.readyState : 'غير معرف');
    }
    
    // 3. فحص HTTP Polling
    console.log('\n🔄 فحص HTTP Polling...');
    if (typeof pollingInterval !== 'undefined' && pollingInterval) {
        console.log('✅ HTTP Polling نشط');
        results.polling = true;
        
        // اختبار Polling API
        try {
            const response = await fetch(`websocket-fallback.php?action=heartbeat&user_id=${currentUser.id}`);
            const data = await response.json();
            if (data.success) {
                console.log('✅ Polling API يعمل بنجاح');
                results.api = true;
            } else {
                console.log('❌ Polling API لا يعمل:', data);
            }
        } catch (error) {
            console.log('❌ خطأ في Polling API:', error.message);
        }
    } else {
        console.log('❌ HTTP Polling غير نشط');
        
        // محاولة اختبار API مباشرة
        try {
            const response = await fetch(`websocket-fallback.php?action=heartbeat&user_id=${currentUser.id}`);
            const data = await response.json();
            if (data.success) {
                console.log('✅ Polling API متاح (لكن غير مفعل)');
                results.api = true;
            }
        } catch (error) {
            console.log('❌ Polling API غير متاح:', error.message);
        }
    }
    
    // 4. فحص المتغيرات المطلوبة
    console.log('\n🔧 فحص المتغيرات المطلوبة...');
    const requiredVars = [
        'enableWebSocket',
        'reconnectAttempts', 
        'maxReconnectAttempts',
        'onlineUsers',
        'messagesDatabase'
    ];
    
    requiredVars.forEach(varName => {
        if (typeof window[varName] !== 'undefined') {
            console.log(`✅ ${varName}:`, window[varName]);
        } else {
            console.log(`❌ ${varName}: غير معرف`);
        }
    });
    
    // 5. التقييم الشامل
    console.log('\n📊 تقييم النظام...');
    if (results.websocket) {
        console.log('🟢 النظام يعمل بـ WebSocket (الأفضل)');
        results.overall = true;
    } else if (results.polling && results.api) {
        console.log('🟡 النظام يعمل بـ HTTP Polling (جيد)');
        results.overall = true;
    } else if (results.api) {
        console.log('🟠 API متاح لكن Polling غير مفعل');
    } else {
        console.log('🔴 النظام لا يعمل');
    }
    
    console.log('\n📋 ملخص النتائج:');
    console.table(results);
    
    return results;
}

// دالة اختبار إرسال رسالة
async function testSendMessage(receiverId = 'test_user', content = 'رسالة اختبار') {
    console.log('📤 اختبار إرسال رسالة...');
    
    if (!currentUser) {
        console.log('❌ لا يوجد مستخدم متصل');
        return false;
    }
    
    try {
        // محاولة عبر WebSocket أولاً
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            console.log('🔄 إرسال عبر WebSocket...');
            websocket.send(JSON.stringify({
                type: 'message',
                receiver_id: receiverId,
                content: content,
                attachments: []
            }));
            console.log('✅ تم الإرسال عبر WebSocket');
            return true;
        }
        
        // محاولة عبر HTTP Polling
        console.log('🔄 إرسال عبر HTTP...');
        const response = await fetch('websocket-fallback.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'send',
                sender_id: currentUser.id,
                receiver_id: receiverId,
                content: content,
                attachments: []
            })
        });
        
        const data = await response.json();
        if (data.success) {
            console.log('✅ تم الإرسال عبر HTTP - معرف الرسالة:', data.message_id);
            return true;
        } else {
            console.log('❌ فشل الإرسال عبر HTTP:', data);
            return false;
        }
        
    } catch (error) {
        console.log('❌ خطأ في إرسال الرسالة:', error);
        return false;
    }
}

// دالة تفعيل HTTP Polling يدوياً
function enablePolling() {
    console.log('🔄 تفعيل HTTP Polling يدوياً...');
    
    if (typeof testHttpPolling === 'function') {
        testHttpPolling();
        console.log('✅ تم تفعيل HTTP Polling');
    } else {
        console.log('❌ دالة testHttpPolling غير متاحة');
    }
}

// دالة عرض حالة النظام
function showSystemStatus() {
    console.log('📊 حالة نظام الرسائل الفورية:');
    console.log('='.repeat(40));
    
    if (typeof showRealtimeStatus === 'function') {
        showRealtimeStatus();
    } else {
        console.log('❌ دالة showRealtimeStatus غير متاحة');
    }
}

// دالة اختبار شاملة
async function runFullTest() {
    console.clear();
    console.log('🧪 اختبار شامل لنظام الرسائل الفورية');
    console.log('='.repeat(50));
    
    // 1. التحقق من النظام
    const results = await verifyRealtimeSystem();
    
    // 2. إذا لم يكن النظام يعمل، حاول تفعيل Polling
    if (!results.overall && results.api) {
        console.log('\n🔧 محاولة تفعيل HTTP Polling...');
        enablePolling();
        
        // انتظار ثانيتين ثم إعادة الفحص
        setTimeout(async () => {
            console.log('\n🔄 إعادة فحص النظام بعد تفعيل Polling...');
            await verifyRealtimeSystem();
        }, 2000);
    }
    
    // 3. اختبار إرسال رسالة
    if (results.overall || results.api) {
        console.log('\n📤 اختبار إرسال رسالة...');
        await testSendMessage();
    }
    
    console.log('\n✅ انتهى الاختبار الشامل');
}

// تصدير الدوال للاستخدام في console
window.verifyRealtimeSystem = verifyRealtimeSystem;
window.testSendMessage = testSendMessage;
window.enablePolling = enablePolling;
window.showSystemStatus = showSystemStatus;
window.runFullTest = runFullTest;

// رسالة ترحيب
console.log('🛠️ أدوات التحقق من نظام الرسائل الفورية محملة!');
console.log('📋 الأوامر المتاحة:');
console.log('   - verifyRealtimeSystem() : فحص شامل للنظام');
console.log('   - testSendMessage() : اختبار إرسال رسالة');
console.log('   - enablePolling() : تفعيل HTTP Polling');
console.log('   - showSystemStatus() : عرض حالة النظام');
console.log('   - runFullTest() : اختبار شامل');
console.log('\n🚀 لبدء الاختبار الشامل، اكتب: runFullTest()');
