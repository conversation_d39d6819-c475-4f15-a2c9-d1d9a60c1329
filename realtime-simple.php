<?php
/**
 * Simple Real-time Messaging API
 * Direct HTTP polling - no WebSocket dependency
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Database connection
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'j5aKN6lz5bsujTcWaYAd';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$userId = $_GET['user_id'] ?? '';

// Handle POST data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postData = json_decode(file_get_contents('php://input'), true);
    if ($postData) {
        $action = $postData['action'] ?? $action;
        $userId = $postData['sender_id'] ?? $userId;
    }
}

switch ($action) {
    case 'init':
        handleInit($pdo, $userId);
        break;
    case 'poll':
        handlePoll($pdo, $userId);
        break;
    case 'send':
        handleSend($pdo, $postData ?? []);
        break;
    case 'status':
        handleStatus($pdo);
        break;
    default:
        echo json_encode(['error' => 'Invalid action', 'available' => ['init', 'poll', 'send', 'status']]);
}

function handleInit($pdo, $userId) {
    if (!$userId) {
        echo json_encode(['error' => 'User ID required']);
        return;
    }
    
    try {
        // Verify user exists
        $stmt = $pdo->prepare("SELECT id, name, role FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo json_encode(['error' => 'User not found or inactive']);
            return;
        }
        
        // Get initial data
        $onlineUsers = getOnlineUsers($pdo, $userId);
        $recentMessages = getRecentMessages($pdo, $userId);
        
        echo json_encode([
            'success' => true,
            'user' => $user,
            'online_users' => $onlineUsers,
            'recent_messages' => $recentMessages,
            'timestamp' => date('Y-m-d H:i:s'),
            'system_status' => 'active'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Init failed: ' . $e->getMessage()]);
    }
}

function handlePoll($pdo, $userId) {
    if (!$userId) {
        echo json_encode(['error' => 'User ID required']);
        return;
    }

    // Handle different timestamp formats
    $lastCheck = $_GET['last_check'] ?? '';
    if (empty($lastCheck)) {
        $lastCheck = date('Y-m-d H:i:s', time() - 30);
    } else {
        // Convert ISO format to MySQL format if needed
        if (strpos($lastCheck, 'T') !== false) {
            $lastCheck = date('Y-m-d H:i:s', strtotime($lastCheck));
        }
    }

    try {
        // Get new messages WHERE THIS USER IS THE RECEIVER
        $stmt = $pdo->prepare("
            SELECT m.*, u.name as sender_name
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            WHERE m.receiver_id = ? AND m.sent_at > ?
            ORDER BY m.sent_at ASC
            LIMIT 50
        ");
        $stmt->execute([$userId, $lastCheck]);
        $newMessages = $stmt->fetchAll();

        // Also get messages WHERE THIS USER IS THE SENDER (for conversation sync)
        $stmt2 = $pdo->prepare("
            SELECT m.*, u.name as receiver_name
            FROM messages m
            LEFT JOIN users u ON m.receiver_id = u.id
            WHERE m.sender_id = ? AND m.sent_at > ?
            ORDER BY m.sent_at ASC
            LIMIT 50
        ");
        $stmt2->execute([$userId, $lastCheck]);
        $sentMessages = $stmt2->fetchAll();

        // Get online users
        $onlineUsers = getOnlineUsers($pdo, $userId);

        // Get all recent messages for debugging (last 10)
        $stmt3 = $pdo->prepare("
            SELECT m.*, u.name as sender_name, u2.name as receiver_name
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            LEFT JOIN users u2 ON m.receiver_id = u2.id
            ORDER BY m.sent_at DESC
            LIMIT 10
        ");
        $stmt3->execute();
        $allRecentMessages = $stmt3->fetchAll();

        echo json_encode([
            'success' => true,
            'new_messages' => $newMessages,
            'sent_messages' => $sentMessages,
            'online_users' => $onlineUsers,
            'timestamp' => date('Y-m-d H:i:s'),
            'poll_interval' => 3000,
            'debug' => [
                'user_id' => $userId,
                'last_check' => $lastCheck,
                'last_check_original' => $_GET['last_check'] ?? 'not provided',
                'received_count' => count($newMessages),
                'sent_count' => count($sentMessages),
                'all_recent_messages' => $allRecentMessages,
                'current_time' => date('Y-m-d H:i:s')
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['error' => 'Poll failed: ' . $e->getMessage()]);
    }
}

function handleSend($pdo, $data) {
    $senderId = $data['sender_id'] ?? '';
    $receiverId = $data['receiver_id'] ?? '';
    $content = $data['content'] ?? '';
    $attachments = $data['attachments'] ?? [];
    
    if (!$senderId || !$receiverId || !$content) {
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    try {
        // Verify both users exist and are active
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE id IN (?, ?) AND is_active = 1");
        $stmt->execute([$senderId, $receiverId]);
        $userCount = $stmt->fetch()['count'];
        
        if ($userCount < 2) {
            echo json_encode(['error' => 'Invalid sender or receiver']);
            return;
        }
        
        // Insert message
        $stmt = $pdo->prepare("
            INSERT INTO messages (sender_id, receiver_id, message, attachments) 
            VALUES (?, ?, ?, ?)
        ");
        
        $attachmentsJson = empty($attachments) ? null : json_encode($attachments);
        $stmt->execute([$senderId, $receiverId, $content, $attachmentsJson]);
        
        $messageId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message_id' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Send failed: ' . $e->getMessage()]);
    }
}

function handleStatus($pdo) {
    try {
        // System health check
        $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE is_active = 1");
        $totalUsers = $stmt->fetch()['total_users'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as total_messages FROM messages WHERE sent_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $recentMessages = $stmt->fetch()['total_messages'];
        
        echo json_encode([
            'success' => true,
            'system_status' => 'healthy',
            'total_users' => $totalUsers,
            'recent_messages_24h' => $recentMessages,
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Status check failed: ' . $e->getMessage()]);
    }
}

function getOnlineUsers($pdo, $excludeUserId) {
    try {
        // Simple approach: all active users are considered "online"
        // Can be enhanced later with actual activity tracking
        $stmt = $pdo->prepare("
            SELECT id, name, role, 'online' as status 
            FROM users 
            WHERE is_active = 1 AND id != ? 
            ORDER BY name 
            LIMIT 50
        ");
        $stmt->execute([$excludeUserId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

function getRecentMessages($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT m.*, u.name as sender_name 
            FROM messages m 
            LEFT JOIN users u ON m.sender_id = u.id 
            WHERE m.receiver_id = ? 
            ORDER BY m.sent_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}
?>
