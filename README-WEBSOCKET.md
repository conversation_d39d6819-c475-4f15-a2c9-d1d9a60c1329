# نظام الرسائل الفورية - WebSocket

## 🚀 المميزات الجديدة

### ✨ الرسائل الفورية
- إرسال واستقبال الرسائل في الوقت الفعلي بدون إعادة تحميل الصفحة
- تأكيد فوري لإرسال الرسائل
- تحديث تلقائي للمحادثات

### 👥 حالة الاتصال
- عرض المستخدمين المتصلين في الوقت الفعلي
- مؤشرات بصرية للحالة (متصل/غير متصل)
- تحديث فوري عند اتصال أو انقطاع المستخدمين

### 🔔 الإشعارات
- إشعارات فورية للرسائل الجديدة
- أصوات مختلفة لاستلام وإرسال الرسائل
- إشعارات منبثقة قابلة للنقر

### ⌨️ مؤشر الكتابة
- عرض "يكتب..." عندما يكتب المستخدم الآخر
- إخفاء تلقائي بعد توقف الكتابة

## 🛠️ التثبيت والتشغيل

### المتطلبات
- PHP 7.4 أو أحدث
- Composer
- مكتبة Ratchet للـ WebSocket

### خطوات التثبيت

#### 1. تثبيت المكتبات
```bash
# على Windows
start-websocket.bat

# على Linux/Mac
chmod +x start-websocket.sh
./start-websocket.sh
```

#### 2. تشغيل الخادم يدوياً
```bash
# تثبيت المكتبات
composer install

# تشغيل خادم WebSocket
php websocket-server.php
```

### 3. التحقق من التشغيل
- يجب أن ترى رسالة: "🚀 خادم WebSocket يعمل على المنفذ 8080"
- افتح التطبيق في المتصفح
- ادخل إلى صفحة الرسائل
- يجب أن ترى في console: "🔗 تم الاتصال بخادم WebSocket"

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
في ملف `websocket-server.php`:
```php
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'j5aKN6lz5bsujTcWaYAd';
```

### إعدادات WebSocket
- المنفذ الافتراضي: 8080
- العنوان: ws://localhost:8080

## 🎯 كيفية الاستخدام

### 1. بدء محادثة
- افتح صفحة الرسائل
- انقر على "رسالة جديدة" أو اختر محادثة موجودة
- ستظهر حالة الاتصال للمستخدم الآخر

### 2. إرسال رسالة
- اكتب رسالتك في الحقل
- اضغط Enter أو انقر على زر الإرسال
- ستسمع صوت تأكيد الإرسال
- الرسالة ستظهر فوراً بدون إعادة تحميل

### 3. استقبال رسالة
- ستسمع صوت إشعار عند وصول رسالة جديدة
- ستظهر رسالة منبثقة إذا لم تكن في المحادثة
- الرسالة ستظهر فوراً في المحادثة المفتوحة

### 4. مؤشر الكتابة
- عندما تكتب، سيرى المستخدم الآخر "يكتب..."
- المؤشر يختفي تلقائياً بعد 3 ثوان من التوقف

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الاتصال بـ WebSocket
```
❌ خطأ في WebSocket: Error in connection establishment
```
**الحل:**
- تأكد من تشغيل خادم WebSocket
- تحقق من أن المنفذ 8080 غير مستخدم
- تأكد من إعدادات الجدار الناري

#### 2. خطأ في قاعدة البيانات
```
❌ خطأ في الاتصال بقاعدة البيانات
```
**الحل:**
- تحقق من إعدادات قاعدة البيانات في websocket-server.php
- تأكد من تشغيل MySQL
- تحقق من صحة بيانات الاتصال

#### 3. الأصوات لا تعمل
**الحل:**
- تأكد من السماح للمتصفح بتشغيل الأصوات
- انقر في أي مكان في الصفحة أولاً (متطلب المتصفح)

### 4. إعادة الاتصال التلقائي
- النظام يحاول إعادة الاتصال تلقائياً 5 مرات
- فترة انتظار متزايدة بين المحاولات

## 📊 مراقبة الأداء

### سجلات الخادم
يعرض خادم WebSocket معلومات مفيدة:
```
🔗 اتصال جديد: 123
✅ تم تسجيل دخول المستخدم: أحمد محمد (ID: user123)
📨 رسالة جديدة من أحمد محمد إلى user456
👋 المستخدم أحمد محمد غادر (ID: 123)
```

### مراقبة في المتصفح
افتح Developer Tools > Console لرؤية:
- حالة الاتصال بـ WebSocket
- الرسائل المرسلة والمستقبلة
- أخطاء الاتصال

## 🔒 الأمان

### الحماية المطبقة
- التحقق من صحة المستخدم قبل السماح بالاتصال
- التحقق من انتماء المستخدم للمركز الصحي
- تشفير الرسائل في قاعدة البيانات
- منع إرسال الرسائل لمستخدمين من مراكز أخرى

### توصيات إضافية
- استخدم HTTPS في الإنتاج
- استخدم WSS (WebSocket Secure) في الإنتاج
- قم بتحديث كلمات المرور بانتظام

## 🚀 التطوير المستقبلي

### ميزات مخططة
- [ ] إرسال الملفات عبر WebSocket
- [ ] مجموعات الدردشة
- [ ] رسائل صوتية
- [ ] مشاركة الموقع
- [ ] تشفير end-to-end

### تحسينات الأداء
- [ ] ضغط الرسائل
- [ ] تخزين مؤقت للرسائل
- [ ] تحسين استهلاك الذاكرة
- [ ] دعم clustering للخوادم المتعددة
