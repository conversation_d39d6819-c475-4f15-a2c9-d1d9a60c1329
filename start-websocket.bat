@echo off
echo 🚀 بدء تشغيل خادم WebSocket للرسائل الفورية...
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أولاً
    pause
    exit /b 1
)

REM التحقق من وجود Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Composer أولاً
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
composer install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

REM تشغيل خادم WebSocket
echo 🌐 تشغيل خادم WebSocket على المنفذ 8080...
echo 📡 يمكن للعملاء الاتصال عبر: ws://localhost:8080
echo.
echo ⚠️  لإيقاف الخادم، اضغط Ctrl+C
echo.

php websocket-server.php

pause
