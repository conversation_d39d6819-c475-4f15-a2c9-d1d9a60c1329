# 🌐 إعداد الرسائل الفورية للموقع المباشر

## 📋 الوضع الحالي

الموقع المباشر `https://www.csmanager.online` يعمل حالياً بـ:
- ✅ **الرسائل التقليدية**: تعمل بشكل طبيعي
- ❌ **الرسائل الفورية**: معطلة مؤقتاً (تحتاج إعداد خادم)

## 🛠️ خطوات تفعيل الرسائل الفورية

### 1. إعداد خادم WebSocket

#### أ. تحديث إعدادات قاعدة البيانات
في ملف `websocket-production.php`:
```php
$host = 'localhost'; // عنوان خادم قاعدة البيانات
$dbname = 'csmanager_db'; // اسم قاعدة البيانات الفعلي
$username = 'csmanager_user'; // مستخدم قاعدة البيانات
$password = 'your_actual_password'; // كلمة المرور الفعلية
```

#### ب. تثبيت المكتبات على الخادم
```bash
# على الخادم المباشر
composer install
```

#### ج. تشغيل خادم WebSocket
```bash
# تشغيل في الخلفية
nohup php websocket-production.php > websocket.log 2>&1 &

# أو استخدام screen
screen -S websocket
php websocket-production.php
# اضغط Ctrl+A ثم D للخروج من screen
```

### 2. إعداد البروكسي (إختياري)

#### أ. إعداد Nginx
```nginx
# إضافة إلى ملف nginx.conf
location /websocket {
    proxy_pass http://localhost:8080;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

#### ب. إعداد Apache
```apache
# إضافة إلى .htaccess أو ملف الإعداد
LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so

ProxyPass /websocket ws://localhost:8080/
ProxyPassReverse /websocket ws://localhost:8080/
```

### 3. إعداد SSL/TLS

#### أ. شهادة SSL للمنفذ 8080
```bash
# إذا كنت تستخدم Let's Encrypt
certbot certonly --standalone -d www.csmanager.online
```

#### ب. تحديث كود WebSocket لاستخدام WSS
في `cs-manager.html` (تم بالفعل):
```javascript
// سيستخدم wss:// تلقائياً للمواقع HTTPS
return 'wss://www.csmanager.online:8080';
```

### 4. فتح المنفذ في الجدار الناري

```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 5. إعداد خدمة النظام (systemd)

إنشاء ملف `/etc/systemd/system/websocket.service`:
```ini
[Unit]
Description=WebSocket Server for CS Manager
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/php websocket-production.php
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

تفعيل الخدمة:
```bash
sudo systemctl enable websocket
sudo systemctl start websocket
sudo systemctl status websocket
```

## 🔧 اختبار التشغيل

### 1. فحص الخادم محلياً
```bash
# فحص أن الخادم يعمل
netstat -tlnp | grep :8080

# فحص السجلات
tail -f websocket.log
```

### 2. اختبار الاتصال
```bash
# اختبار WebSocket
wscat -c ws://localhost:8080
```

### 3. فحص من المتصفح
- افتح Developer Tools > Console
- ابحث عن رسائل WebSocket
- يجب أن ترى: "🔗 تم الاتصال بخادم WebSocket"

## 🚨 استكشاف الأخطاء

### مشكلة: "Connection refused"
```bash
# فحص أن الخادم يعمل
ps aux | grep websocket
sudo netstat -tlnp | grep :8080
```

### مشكلة: "SSL handshake failed"
```bash
# فحص شهادة SSL
openssl s_client -connect www.csmanager.online:8080
```

### مشكلة: "Database connection failed"
```bash
# فحص الاتصال بقاعدة البيانات
mysql -h localhost -u csmanager_user -p csmanager_db
```

## 📊 مراقبة الأداء

### سجلات مفيدة
```bash
# سجلات WebSocket
tail -f websocket.log

# سجلات النظام
journalctl -u websocket -f

# استخدام الذاكرة
ps aux | grep websocket
```

### إحصائيات الاتصالات
```bash
# عدد الاتصالات النشطة
netstat -an | grep :8080 | grep ESTABLISHED | wc -l
```

## 🔄 إعادة التشغيل

### إعادة تشغيل الخدمة
```bash
sudo systemctl restart websocket
```

### إعادة تشغيل يدوي
```bash
# إيقاف العملية
pkill -f websocket-production.php

# تشغيل جديد
nohup php websocket-production.php > websocket.log 2>&1 &
```

## ✅ التحقق من النجاح

عند نجاح الإعداد:
1. في سجلات الخادم: "🚀 خادم WebSocket للإنتاج يعمل"
2. في التطبيق: "🟢 رسائل فورية" بدلاً من "🔴 رسائل تقليدية"
3. اختفاء الإشعار الأزرق في صفحة الرسائل
4. عمل جميع الميزات الفورية

## 📞 الدعم

إذا واجهت مشاكل في الإعداد:
1. تحقق من السجلات أولاً
2. تأكد من إعدادات قاعدة البيانات
3. فحص الجدار الناري والمنافذ
4. اختبار الاتصال خطوة بخطوة
