<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .user-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .user1 { border-top: 4px solid #007bff; }
        .user2 { border-top: 4px solid #28a745; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .connecting { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .messages {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .send-area {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        input[type="text"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار الرسائل الفورية - مستخدمين متعددين</h1>
    
    <div class="container">
        <!-- User 1 Panel -->
        <div class="user-panel user1">
            <h3>👤 المستخدم الأول (nurse_687b80778c2d8)</h3>
            
            <div id="status1" class="status connecting">🔄 جاري الاتصال...</div>
            
            <div class="send-area">
                <input type="text" id="message1" placeholder="اكتب رسالة للمستخدم الثاني..." />
                <button onclick="sendMessage1()">إرسال</button>
            </div>
            
            <h4>📨 الرسائل المستقبلة:</h4>
            <div id="messages1" class="messages"></div>
            
            <h4>📋 سجل النشاط:</h4>
            <div id="log1" class="log"></div>
        </div>
        
        <!-- User 2 Panel -->
        <div class="user-panel user2">
            <h3>👤 المستخدم الثاني (nurse_001)</h3>
            
            <div id="status2" class="status connecting">🔄 جاري الاتصال...</div>
            
            <div class="send-area">
                <input type="text" id="message2" placeholder="اكتب رسالة للمستخدم الأول..." />
                <button onclick="sendMessage2()">إرسال</button>
            </div>
            
            <h4>📨 الرسائل المستقبلة:</h4>
            <div id="messages2" class="messages"></div>
            
            <h4>📋 سجل النشاط:</h4>
            <div id="log2" class="log"></div>
        </div>
    </div>

    <script src="simple-realtime.js"></script>
    <script>
        // إعداد المستخدمين
        const user1 = {
            id: 'nurse_687b80778c2d8',
            name: 'المستخدم الأول',
            role: 'nurse'
        };
        
        const user2 = {
            id: 'nurse_001',
            name: 'المستخدم الثاني',
            role: 'nurse'
        };
        
        let realtime1 = null;
        let realtime2 = null;
        
        // دوال التسجيل
        function log1(message) {
            const logElement = document.getElementById('log1');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`User1: ${message}`);
        }
        
        function log2(message) {
            const logElement = document.getElementById('log2');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`User2: ${message}`);
        }
        
        // دوال تحديث الحالة
        function updateStatus1(status, message) {
            const statusElement = document.getElementById('status1');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        function updateStatus2(status, message) {
            const statusElement = document.getElementById('status2');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        // دوال عرض الرسائل
        function addMessage1(message, type) {
            const messagesElement = document.getElementById('messages1');
            const timestamp = new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                margin: 5px 0;
                padding: 8px;
                border-radius: 4px;
                background: ${type === 'received' ? '#e3f2fd' : '#f3e5f5'};
                border-left: 3px solid ${type === 'received' ? '#2196f3' : '#9c27b0'};
            `;
            messageDiv.innerHTML = `
                <strong>${type === 'received' ? 'من' : 'إلى'} ${message.sender_name}:</strong><br>
                ${message.content}<br>
                <small>${timestamp}</small>
            `;
            messagesElement.appendChild(messageDiv);
            messagesElement.scrollTop = messagesElement.scrollHeight;
        }
        
        function addMessage2(message, type) {
            const messagesElement = document.getElementById('messages2');
            const timestamp = new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                margin: 5px 0;
                padding: 8px;
                border-radius: 4px;
                background: ${type === 'received' ? '#e8f5e8' : '#fff3e0'};
                border-left: 3px solid ${type === 'received' ? '#4caf50' : '#ff9800'};
            `;
            messageDiv.innerHTML = `
                <strong>${type === 'received' ? 'من' : 'إلى'} ${message.sender_name}:</strong><br>
                ${message.content}<br>
                <small>${timestamp}</small>
            `;
            messagesElement.appendChild(messageDiv);
            messagesElement.scrollTop = messagesElement.scrollHeight;
        }
        
        // دوال الإرسال
        async function sendMessage1() {
            const input = document.getElementById('message1');
            const content = input.value.trim();
            
            if (!content || !realtime1) return;
            
            try {
                const success = await realtime1.sendMessage(user2.id, content);
                if (success) {
                    log1(`✅ تم إرسال الرسالة: ${content}`);
                    input.value = '';
                } else {
                    log1(`❌ فشل في إرسال الرسالة`);
                }
            } catch (error) {
                log1(`❌ خطأ في الإرسال: ${error.message}`);
            }
        }
        
        async function sendMessage2() {
            const input = document.getElementById('message2');
            const content = input.value.trim();
            
            if (!content || !realtime2) return;
            
            try {
                const success = await realtime2.sendMessage(user1.id, content);
                if (success) {
                    log2(`✅ تم إرسال الرسالة: ${content}`);
                    input.value = '';
                } else {
                    log2(`❌ فشل في إرسال الرسالة`);
                }
            } catch (error) {
                log2(`❌ خطأ في الإرسال: ${error.message}`);
            }
        }
        
        // تهيئة المستخدم الأول
        async function initUser1() {
            log1('🚀 تهيئة المستخدم الأول...');
            
            try {
                realtime1 = new SimpleRealtime();
                
                realtime1.onMessage((message) => {
                    if (message.type === 'received') {
                        log1(`📨 رسالة جديدة من ${message.sender_name}: ${message.content}`);
                        addMessage1(message, 'received');
                    } else {
                        log1(`📤 رسالة مرسلة إلى ${message.sender_name}: ${message.content}`);
                        addMessage1(message, 'sent');
                    }
                });
                
                realtime1.onStatusChange((event) => {
                    if (event.type === 'status_change') {
                        updateStatus1('connected', '🟢 متصل');
                        log1(`🔄 تغيير الحالة: ${event.status}`);
                    }
                });
                
                const success = await realtime1.init(user1);
                if (success) {
                    log1('✅ تم تهيئة النظام بنجاح');
                    updateStatus1('connected', '🟢 متصل');
                } else {
                    throw new Error('فشل في التهيئة');
                }
            } catch (error) {
                log1(`❌ خطأ في التهيئة: ${error.message}`);
                updateStatus1('error', '❌ خطأ');
            }
        }
        
        // تهيئة المستخدم الثاني
        async function initUser2() {
            log2('🚀 تهيئة المستخدم الثاني...');
            
            try {
                realtime2 = new SimpleRealtime();
                
                realtime2.onMessage((message) => {
                    if (message.type === 'received') {
                        log2(`📨 رسالة جديدة من ${message.sender_name}: ${message.content}`);
                        addMessage2(message, 'received');
                    } else {
                        log2(`📤 رسالة مرسلة إلى ${message.sender_name}: ${message.content}`);
                        addMessage2(message, 'sent');
                    }
                });
                
                realtime2.onStatusChange((event) => {
                    if (event.type === 'status_change') {
                        updateStatus2('connected', '🟢 متصل');
                        log2(`🔄 تغيير الحالة: ${event.status}`);
                    }
                });
                
                const success = await realtime2.init(user2);
                if (success) {
                    log2('✅ تم تهيئة النظام بنجاح');
                    updateStatus2('connected', '🟢 متصل');
                } else {
                    throw new Error('فشل في التهيئة');
                }
            } catch (error) {
                log2(`❌ خطأ في التهيئة: ${error.message}`);
                updateStatus2('error', '❌ خطأ');
            }
        }
        
        // معالجة Enter للإرسال
        document.getElementById('message1').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage1();
        });
        
        document.getElementById('message2').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage2();
        });
        
        // تهيئة تلقائية
        window.addEventListener('load', () => {
            console.log('🧪 بدء اختبار الرسائل الفورية بين مستخدمين');
            
            setTimeout(initUser1, 1000);
            setTimeout(initUser2, 2000);
        });
    </script>
</body>
</html>
