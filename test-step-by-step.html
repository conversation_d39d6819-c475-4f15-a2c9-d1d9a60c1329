<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الرسائل الفورية خطوة بخطوة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .step.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .step.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .step.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .result.success { background: #d4edda; color: #155724; }
        .result.error { background: #f8d7da; color: #721c24; }
        .result.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الرسائل الفورية خطوة بخطوة</h1>
        
        <div class="step" id="step1">
            <h3>الخطوة 1: اختبار API</h3>
            <p>التحقق من أن API يعمل بشكل صحيح</p>
            <button onclick="runStep1()">تشغيل الخطوة 1</button>
            <div id="result1" class="result" style="display: none;"></div>
        </div>
        
        <div class="step" id="step2">
            <h3>الخطوة 2: إرسال رسالة اختبار</h3>
            <p>إرسال رسالة من مستخدم إلى آخر</p>
            <button onclick="runStep2()" id="btn2" disabled>تشغيل الخطوة 2</button>
            <div id="result2" class="result" style="display: none;"></div>
        </div>
        
        <div class="step" id="step3">
            <h3>الخطوة 3: اختبار استقبال الرسالة</h3>
            <p>التحقق من أن المستقبل يحصل على الرسالة</p>
            <button onclick="runStep3()" id="btn3" disabled>تشغيل الخطوة 3</button>
            <div id="result3" class="result" style="display: none;"></div>
        </div>
        
        <div class="step" id="step4">
            <h3>الخطوة 4: اختبار النظام الفوري</h3>
            <p>تشغيل النظام الفوري واختبار Polling</p>
            <button onclick="runStep4()" id="btn4" disabled>تشغيل الخطوة 4</button>
            <div id="result4" class="result" style="display: none;"></div>
        </div>
        
        <div class="step" id="step5">
            <h3>الخطوة 5: اختبار شامل</h3>
            <p>اختبار كامل للرسائل الفورية</p>
            <button onclick="runStep5()" id="btn5" disabled>تشغيل الخطوة 5</button>
            <div id="result5" class="result" style="display: none;"></div>
        </div>
        
        <div class="step">
            <h3>📋 سجل مفصل</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="simple-realtime.js"></script>
    <script>
        const logElement = document.getElementById('log');
        let testData = {};
        
        // دالة تسجيل الرسائل
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
        }
        
        // تحديث نتيجة الخطوة
        function updateStepResult(stepNum, status, message) {
            const step = document.getElementById(`step${stepNum}`);
            const result = document.getElementById(`result${stepNum}`);
            
            step.className = `step ${status}`;
            result.className = `result ${status}`;
            result.textContent = message;
            result.style.display = 'block';
            
            // تفعيل الخطوة التالية
            if (status === 'success' && stepNum < 5) {
                const nextBtn = document.getElementById(`btn${stepNum + 1}`);
                if (nextBtn) {
                    nextBtn.disabled = false;
                }
            }
        }
        
        // الخطوة 1: اختبار API
        async function runStep1() {
            log('🔄 بدء الخطوة 1: اختبار API...');
            document.getElementById('step1').className = 'step active';
            
            try {
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API يعمل بنجاح');
                    log(`📊 المستخدمين: ${data.total_users}, الرسائل: ${data.recent_messages_24h}`);
                    
                    testData.apiWorking = true;
                    updateStepResult(1, 'success', `API يعمل - ${data.total_users} مستخدم`);
                } else {
                    throw new Error('API response indicates failure');
                }
            } catch (error) {
                log('❌ خطأ في API: ' + error.message);
                updateStepResult(1, 'error', 'فشل في API: ' + error.message);
            }
        }
        
        // الخطوة 2: إرسال رسالة اختبار
        async function runStep2() {
            log('🔄 بدء الخطوة 2: إرسال رسالة اختبار...');
            document.getElementById('step2').className = 'step active';

            // استخدام مستخدمين موجودين بالفعل
            const senderId = 'nurse_687b80778c2d8';
            const receiverId = 'admin_001'; // تغيير إلى admin بدلاً من nurse_001
            const content = 'رسالة اختبار فورية - ' + new Date().toLocaleTimeString();
            
            try {
                const response = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: senderId,
                        receiver_id: receiverId,
                        content: content,
                        attachments: []
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('✅ تم إرسال الرسالة بنجاح - ID: ' + data.message_id);
                    
                    testData.messageId = data.message_id;
                    testData.messageTime = data.timestamp;
                    testData.senderId = senderId;
                    testData.receiverId = receiverId;
                    testData.content = content;
                    
                    updateStepResult(2, 'success', `تم الإرسال - ID: ${data.message_id}`);
                } else {
                    throw new Error(data.error || 'Send failed');
                }
            } catch (error) {
                log('❌ خطأ في إرسال الرسالة: ' + error.message);
                updateStepResult(2, 'error', 'فشل في الإرسال: ' + error.message);
            }
        }
        
        // الخطوة 3: اختبار استقبال الرسالة
        async function runStep3() {
            log('🔄 بدء الخطوة 3: اختبار استقبال الرسالة...');
            document.getElementById('step3').className = 'step active';
            
            if (!testData.messageTime || !testData.receiverId) {
                updateStepResult(3, 'error', 'لا توجد بيانات رسالة للاختبار');
                return;
            }
            
            try {
                // انتظار ثانيتين ثم اختبار الاستقبال
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const response = await fetch(`realtime-simple.php?action=poll&user_id=${testData.receiverId}&last_check=${encodeURIComponent(testData.messageTime)}`);
                const data = await response.json();
                
                if (data.success) {
                    const newMessages = data.new_messages || [];
                    log(`📬 تم العثور على ${newMessages.length} رسالة جديدة`);
                    
                    // البحث عن رسالتنا
                    const ourMessage = newMessages.find(msg => msg.id == testData.messageId);
                    
                    if (ourMessage) {
                        log('✅ تم العثور على الرسالة المرسلة!');
                        log(`📨 الرسالة: ${ourMessage.message}`);
                        updateStepResult(3, 'success', 'تم استقبال الرسالة بنجاح');
                    } else {
                        log('⚠️ لم يتم العثور على الرسالة المرسلة');
                        log('🔍 الرسائل الموجودة:');
                        newMessages.forEach(msg => {
                            log(`  - ID: ${msg.id}, من: ${msg.sender_name}, النص: ${msg.message}`);
                        });
                        updateStepResult(3, 'error', 'لم يتم العثور على الرسالة');
                    }
                } else {
                    throw new Error(data.error || 'Poll failed');
                }
            } catch (error) {
                log('❌ خطأ في اختبار الاستقبال: ' + error.message);
                updateStepResult(3, 'error', 'فشل في الاستقبال: ' + error.message);
            }
        }
        
        // الخطوة 4: اختبار النظام الفوري
        async function runStep4() {
            log('🔄 بدء الخطوة 4: اختبار النظام الفوري...');
            document.getElementById('step4').className = 'step active';
            
            try {
                const testUser = {
                    id: 'nurse_687b80778c2d8',
                    name: 'مستخدم اختبار',
                    role: 'nurse'
                };
                
                const realtime = new SimpleRealtime();
                
                // إعداد معالج الرسائل
                let messageReceived = false;
                realtime.onMessage((message) => {
                    log(`📨 رسالة فورية: ${message.content} من ${message.sender_name}`);
                    messageReceived = true;
                });
                
                // تهيئة النظام
                const success = await realtime.init(testUser);
                
                if (success) {
                    log('✅ تم تهيئة النظام الفوري بنجاح');
                    
                    // اختبار polling لمدة 10 ثوان
                    let pollCount = 0;
                    const pollTest = setInterval(() => {
                        pollCount++;
                        log(`🔄 Poll #${pollCount}`);
                        
                        if (pollCount >= 3) {
                            clearInterval(pollTest);
                            realtime.stop();
                            
                            if (messageReceived) {
                                updateStepResult(4, 'success', 'النظام الفوري يعمل ويستقبل الرسائل');
                            } else {
                                updateStepResult(4, 'success', 'النظام الفوري يعمل (لا توجد رسائل جديدة)');
                            }
                        }
                    }, 3000);
                    
                } else {
                    throw new Error('فشل في تهيئة النظام الفوري');
                }
            } catch (error) {
                log('❌ خطأ في النظام الفوري: ' + error.message);
                updateStepResult(4, 'error', 'فشل في النظام الفوري: ' + error.message);
            }
        }
        
        // الخطوة 5: اختبار شامل
        async function runStep5() {
            log('🔄 بدء الخطوة 5: اختبار شامل...');
            document.getElementById('step5').className = 'step active';
            
            try {
                // إنشاء مستخدمين للاختبار
                const user1 = { id: 'nurse_687b80778c2d8', name: 'مستخدم 1', role: 'nurse' };
                const user2 = { id: 'nurse_001', name: 'مستخدم 2', role: 'nurse' };
                
                const realtime1 = new SimpleRealtime();
                const realtime2 = new SimpleRealtime();
                
                let user2ReceivedMessage = false;
                
                // إعداد معالج الرسائل للمستخدم 2
                realtime2.onMessage((message) => {
                    if (message.type === 'received') {
                        log(`✅ المستخدم 2 استقبل رسالة: ${message.content}`);
                        user2ReceivedMessage = true;
                    }
                });
                
                // تهيئة المستخدمين
                const init1 = await realtime1.init(user1);
                const init2 = await realtime2.init(user2);
                
                if (init1 && init2) {
                    log('✅ تم تهيئة كلا المستخدمين');
                    
                    // انتظار ثانيتين ثم إرسال رسالة
                    setTimeout(async () => {
                        const testMessage = 'رسالة اختبار شاملة - ' + new Date().toLocaleTimeString();
                        const sent = await realtime1.sendMessage(user2.id, testMessage);
                        
                        if (sent) {
                            log('✅ تم إرسال رسالة الاختبار الشاملة');
                            
                            // انتظار 10 ثوان للتحقق من الاستقبال
                            setTimeout(() => {
                                realtime1.stop();
                                realtime2.stop();
                                
                                if (user2ReceivedMessage) {
                                    updateStepResult(5, 'success', '🎉 الاختبار الشامل نجح! الرسائل الفورية تعمل');
                                } else {
                                    updateStepResult(5, 'error', 'تم الإرسال لكن لم يتم الاستقبال الفوري');
                                }
                            }, 10000);
                        } else {
                            throw new Error('فشل في إرسال رسالة الاختبار');
                        }
                    }, 2000);
                    
                } else {
                    throw new Error('فشل في تهيئة المستخدمين');
                }
            } catch (error) {
                log('❌ خطأ في الاختبار الشامل: ' + error.message);
                updateStepResult(5, 'error', 'فشل في الاختبار الشامل: ' + error.message);
            }
        }
        
        // بدء الاختبار تلقائياً
        window.addEventListener('load', () => {
            log('🚀 بدء اختبار الرسائل الفورية خطوة بخطوة');
            setTimeout(runStep1, 1000);
        });
    </script>
</body>
</html>
