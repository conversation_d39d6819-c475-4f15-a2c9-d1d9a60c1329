<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل الرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشاكل الرسائل الفورية</h1>
        
        <div class="test-section info">
            <h3>📋 معلومات التشخيص</h3>
            <p><strong>الهدف:</strong> تحديد سبب عدم عمل الرسائل الفورية</p>
            <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="grid">
            <div>
                <div class="test-section">
                    <h3>🔌 اختبار API</h3>
                    <button onclick="testAPI()">اختبار الاتصال</button>
                    <button onclick="testDatabase()">اختبار قاعدة البيانات</button>
                    <div id="apiResult"></div>
                </div>
                
                <div class="test-section">
                    <h3>👥 اختبار المستخدمين</h3>
                    <select id="userSelect">
                        <option value="">اختر مستخدم...</option>
                    </select>
                    <button onclick="testUser()">اختبار المستخدم</button>
                    <div id="userResult"></div>
                </div>
                
                <div class="test-section">
                    <h3>📨 اختبار الرسائل</h3>
                    <input type="text" id="testMessage" placeholder="رسالة اختبار" value="رسالة اختبار">
                    <select id="receiverSelect">
                        <option value="">اختر المستقبل...</option>
                    </select>
                    <button onclick="sendTestMessage()">إرسال رسالة اختبار</button>
                    <div id="messageResult"></div>
                </div>
            </div>
            
            <div>
                <div class="test-section">
                    <h3>🔄 اختبار Polling</h3>
                    <button onclick="startPollingTest()">بدء اختبار Polling</button>
                    <button onclick="stopPollingTest()">إيقاف الاختبار</button>
                    <div id="pollingResult"></div>
                </div>
                
                <div class="test-section">
                    <h3>📊 إحصائيات الرسائل</h3>
                    <button onclick="getMessageStats()">عرض الإحصائيات</button>
                    <div id="statsResult"></div>
                </div>
                
                <div class="test-section">
                    <h3>🗄️ فحص قاعدة البيانات</h3>
                    <button onclick="checkDatabaseStructure()">فحص الهيكل</button>
                    <button onclick="getRecentMessages()">آخر الرسائل</button>
                    <div id="dbResult"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 سجل التشخيص المفصل</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <button onclick="runFullDiagnostic()">تشخيص شامل</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let pollingTestInterval = null;
        const logElement = document.getElementById('log');
        
        // تحديث الوقت
        document.getElementById('currentTime').textContent = new Date().toLocaleString('ar');
        
        // دالة تسجيل الرسائل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
        }
        
        // تحديث نتيجة الاختبار
        function updateResult(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `test-section ${status}`;
            element.innerHTML = `<h4>النتيجة:</h4><p>${message}</p>`;
        }
        
        // اختبار API الأساسي
        async function testAPI() {
            log('🔌 اختبار API الأساسي...');
            
            try {
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API يعمل بنجاح');
                    log(`📊 المستخدمين: ${data.total_users}, الرسائل: ${data.recent_messages_24h}`);
                    updateResult('apiResult', 'success', `API يعمل - ${data.total_users} مستخدم، ${data.recent_messages_24h} رسالة`);
                    
                    // تحميل قائمة المستخدمين
                    await loadUsers();
                } else {
                    log('❌ API لا يعمل: ' + JSON.stringify(data));
                    updateResult('apiResult', 'error', 'API لا يعمل: ' + JSON.stringify(data));
                }
            } catch (error) {
                log('❌ خطأ في API: ' + error.message);
                updateResult('apiResult', 'error', 'خطأ في الاتصال: ' + error.message);
            }
        }
        
        // تحميل قائمة المستخدمين
        async function loadUsers() {
            try {
                const response = await fetch('realtime-simple.php?action=poll&user_id=test&last_check=2024-01-01');
                const data = await response.json();
                
                if (data.success && data.online_users) {
                    const userSelect = document.getElementById('userSelect');
                    const receiverSelect = document.getElementById('receiverSelect');
                    
                    userSelect.innerHTML = '<option value="">اختر مستخدم...</option>';
                    receiverSelect.innerHTML = '<option value="">اختر المستقبل...</option>';
                    
                    data.online_users.forEach(user => {
                        const option1 = new Option(`${user.name} (${user.id})`, user.id);
                        const option2 = new Option(`${user.name} (${user.id})`, user.id);
                        userSelect.add(option1);
                        receiverSelect.add(option2);
                    });
                    
                    log(`👥 تم تحميل ${data.online_users.length} مستخدم`);
                }
            } catch (error) {
                log('❌ خطأ في تحميل المستخدمين: ' + error.message);
            }
        }
        
        // اختبار مستخدم محدد
        async function testUser() {
            const userId = document.getElementById('userSelect').value;
            if (!userId) {
                alert('يرجى اختيار مستخدم');
                return;
            }
            
            log(`👤 اختبار المستخدم: ${userId}`);
            
            try {
                const response = await fetch(`realtime-simple.php?action=init&user_id=${userId}`);
                const data = await response.json();
                
                if (data.success) {
                    log('✅ المستخدم صحيح ويمكن تهيئته');
                    log(`📊 رسائل حديثة: ${data.recent_messages?.length || 0}`);
                    updateResult('userResult', 'success', `المستخدم صحيح - ${data.recent_messages?.length || 0} رسالة حديثة`);
                } else {
                    log('❌ فشل في تهيئة المستخدم: ' + data.error);
                    updateResult('userResult', 'error', 'فشل في تهيئة المستخدم: ' + data.error);
                }
            } catch (error) {
                log('❌ خطأ في اختبار المستخدم: ' + error.message);
                updateResult('userResult', 'error', 'خطأ في اختبار المستخدم: ' + error.message);
            }
        }
        
        // إرسال رسالة اختبار
        async function sendTestMessage() {
            const senderId = document.getElementById('userSelect').value;
            const receiverId = document.getElementById('receiverSelect').value;
            const content = document.getElementById('testMessage').value;
            
            if (!senderId || !receiverId || !content) {
                alert('يرجى ملء جميع الحقول');
                return;
            }
            
            log(`📤 إرسال رسالة من ${senderId} إلى ${receiverId}: ${content}`);
            
            try {
                const response = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: senderId,
                        receiver_id: receiverId,
                        content: content,
                        attachments: []
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('✅ تم إرسال الرسالة بنجاح - ID: ' + data.message_id);
                    updateResult('messageResult', 'success', `تم الإرسال بنجاح - ID: ${data.message_id}`);
                    
                    // اختبار استقبال الرسالة
                    setTimeout(() => testMessageReceived(receiverId, data.timestamp), 2000);
                } else {
                    log('❌ فشل في إرسال الرسالة: ' + data.error);
                    updateResult('messageResult', 'error', 'فشل في الإرسال: ' + data.error);
                }
            } catch (error) {
                log('❌ خطأ في إرسال الرسالة: ' + error.message);
                updateResult('messageResult', 'error', 'خطأ في الإرسال: ' + error.message);
            }
        }
        
        // اختبار استقبال الرسالة
        async function testMessageReceived(receiverId, timestamp) {
            log(`📬 اختبار استقبال الرسالة للمستخدم: ${receiverId}`);
            
            try {
                const response = await fetch(`realtime-simple.php?action=poll&user_id=${receiverId}&last_check=${timestamp}`);
                const data = await response.json();
                
                if (data.success) {
                    const newMessages = data.new_messages || [];
                    if (newMessages.length > 0) {
                        log(`✅ تم استقبال ${newMessages.length} رسالة جديدة`);
                        newMessages.forEach(msg => {
                            log(`📨 رسالة: ${msg.message} من ${msg.sender_name}`);
                        });
                        updateResult('messageResult', 'success', `تم الإرسال والاستقبال بنجاح - ${newMessages.length} رسالة`);
                    } else {
                        log('⚠️ لم يتم استقبال رسائل جديدة');
                        updateResult('messageResult', 'warning', 'تم الإرسال لكن لم يتم الاستقبال');
                    }
                } else {
                    log('❌ فشل في اختبار الاستقبال: ' + data.error);
                }
            } catch (error) {
                log('❌ خطأ في اختبار الاستقبال: ' + error.message);
            }
        }
        
        // بدء اختبار Polling
        function startPollingTest() {
            const userId = document.getElementById('userSelect').value;
            if (!userId) {
                alert('يرجى اختيار مستخدم أولاً');
                return;
            }
            
            log(`🔄 بدء اختبار Polling للمستخدم: ${userId}`);
            
            let pollCount = 0;
            pollingTestInterval = setInterval(async () => {
                pollCount++;
                try {
                    const lastCheck = new Date(Date.now() - 10000).toISOString();
                    const response = await fetch(`realtime-simple.php?action=poll&user_id=${userId}&last_check=${lastCheck}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        log(`🔄 Poll #${pollCount}: ${data.new_messages?.length || 0} رسائل جديدة، ${data.online_users?.length || 0} مستخدم متصل`);
                        updateResult('pollingResult', 'info', `Poll #${pollCount} نجح - ${data.new_messages?.length || 0} رسائل`);
                    } else {
                        log(`❌ Poll #${pollCount} فشل: ${data.error}`);
                        updateResult('pollingResult', 'error', `Poll #${pollCount} فشل`);
                    }
                } catch (error) {
                    log(`❌ Poll #${pollCount} خطأ: ${error.message}`);
                    updateResult('pollingResult', 'error', `Poll #${pollCount} خطأ`);
                }
            }, 3000);
            
            updateResult('pollingResult', 'info', 'جاري تشغيل اختبار Polling...');
        }
        
        // إيقاف اختبار Polling
        function stopPollingTest() {
            if (pollingTestInterval) {
                clearInterval(pollingTestInterval);
                pollingTestInterval = null;
                log('⏹️ تم إيقاف اختبار Polling');
                updateResult('pollingResult', 'warning', 'تم إيقاف اختبار Polling');
            }
        }
        
        // عرض إحصائيات الرسائل
        async function getMessageStats() {
            log('📊 جلب إحصائيات الرسائل...');
            
            try {
                // محاولة الحصول على إحصائيات من API
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    const stats = `
                        المستخدمين النشطين: ${data.total_users}
                        الرسائل خلال 24 ساعة: ${data.recent_messages_24h}
                        حالة النظام: ${data.system_status}
                        الإصدار: ${data.version}
                    `;
                    log('📊 إحصائيات النظام:\n' + stats);
                    updateResult('statsResult', 'success', stats);
                } else {
                    updateResult('statsResult', 'error', 'فشل في جلب الإحصائيات');
                }
            } catch (error) {
                log('❌ خطأ في جلب الإحصائيات: ' + error.message);
                updateResult('statsResult', 'error', 'خطأ في جلب الإحصائيات');
            }
        }
        
        // فحص هيكل قاعدة البيانات
        async function checkDatabaseStructure() {
            log('🗄️ فحص هيكل قاعدة البيانات...');
            updateResult('dbResult', 'info', 'جاري فحص قاعدة البيانات...');
            
            // هذا اختبار بسيط - في الواقع نحتاج endpoint خاص لفحص الهيكل
            try {
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ قاعدة البيانات متصلة ويمكن الوصول إليها');
                    updateResult('dbResult', 'success', 'قاعدة البيانات متصلة بنجاح');
                } else {
                    log('❌ مشكلة في قاعدة البيانات');
                    updateResult('dbResult', 'error', 'مشكلة في قاعدة البيانات');
                }
            } catch (error) {
                log('❌ خطأ في فحص قاعدة البيانات: ' + error.message);
                updateResult('dbResult', 'error', 'خطأ في الاتصال بقاعدة البيانات');
            }
        }
        
        // جلب آخر الرسائل
        async function getRecentMessages() {
            log('📨 جلب آخر الرسائل...');
            
            try {
                const response = await fetch('realtime-simple.php?action=poll&user_id=admin_001&last_check=2024-01-01');
                const data = await response.json();
                
                if (data.success) {
                    const messages = data.new_messages || [];
                    log(`📨 تم العثور على ${messages.length} رسالة`);
                    
                    if (messages.length > 0) {
                        messages.slice(0, 5).forEach(msg => {
                            log(`📝 ${msg.sent_at}: ${msg.sender_name} → ${msg.message}`);
                        });
                        updateResult('dbResult', 'success', `تم العثور على ${messages.length} رسالة`);
                    } else {
                        updateResult('dbResult', 'warning', 'لا توجد رسائل حديثة');
                    }
                } else {
                    updateResult('dbResult', 'error', 'فشل في جلب الرسائل');
                }
            } catch (error) {
                log('❌ خطأ في جلب الرسائل: ' + error.message);
                updateResult('dbResult', 'error', 'خطأ في جلب الرسائل');
            }
        }
        
        // تشخيص شامل
        async function runFullDiagnostic() {
            log('🚀 بدء التشخيص الشامل...');
            clearLog();
            
            // تشغيل جميع الاختبارات بالتسلسل
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (document.getElementById('userSelect').options.length > 1) {
                document.getElementById('userSelect').selectedIndex = 1;
                await testUser();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            await getMessageStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await checkDatabaseStructure();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await getRecentMessages();
            
            log('✅ انتهى التشخيص الشامل');
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
