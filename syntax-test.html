<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص الأخطاء النحوية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص الأخطاء النحوية</h1>
        <p>هذه الصفحة تفحص الأخطاء النحوية في JavaScript</p>
        
        <div id="log" class="log">
            جاري تحميل فاحص الأخطاء...
        </div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        
        // Override console methods to display in page
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also log to original console
            if (type === 'error') {
                originalError(message);
            } else if (type === 'warn') {
                originalWarn(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = (message) => addToLog(message, 'info');
        console.error = (message) => addToLog(message, 'error');
        console.warn = (message) => addToLog(message, 'warn');
        
        // Clear initial message
        logElement.textContent = '';
        
        // Load and run syntax validator
        const script = document.createElement('script');
        script.src = 'syntax-validator.js';
        script.onload = () => {
            console.log('✅ تم تحميل فاحص الأخطاء بنجاح');
        };
        script.onerror = () => {
            console.error('❌ فشل في تحميل فاحص الأخطاء');
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
