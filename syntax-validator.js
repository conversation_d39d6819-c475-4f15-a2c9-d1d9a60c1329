// Simple JavaScript syntax validator
console.log('🔍 بدء فحص الأخطاء النحوية...');

// Test basic JavaScript syntax
try {
    // Test 1: Basic function declaration
    function testFunction() {
        console.log('Test function works');
    }
    testFunction();
    console.log('✅ Test 1: Basic function declaration - OK');
    
    // Test 2: Async function
    async function testAsync() {
        return Promise.resolve('async works');
    }
    testAsync().then(result => {
        console.log('✅ Test 2: Async function - OK:', result);
    });
    
    // Test 3: Arrow functions
    const testArrow = () => {
        return 'arrow works';
    };
    console.log('✅ Test 3: Arrow function - OK:', testArrow());
    
    // Test 4: Object destructuring
    const { location } = window;
    console.log('✅ Test 4: Object destructuring - OK');
    
    // Test 5: Template literals
    const message = `Template literal works: ${new Date().toLocaleTimeString()}`;
    console.log('✅ Test 5: Template literals - OK:', message);
    
    // Test 6: Try-catch blocks
    try {
        throw new Error('Test error');
    } catch (error) {
        console.log('✅ Test 6: Try-catch blocks - OK');
    }
    
    // Test 7: Classes
    class TestClass {
        constructor() {
            this.name = 'test';
        }
        
        method() {
            return this.name;
        }
    }
    
    const instance = new TestClass();
    console.log('✅ Test 7: Classes - OK:', instance.method());
    
    console.log('🎉 جميع الاختبارات نجحت - لا توجد مشاكل نحوية أساسية');
    
} catch (error) {
    console.error('❌ خطأ نحوي:', error);
    console.error('Stack trace:', error.stack);
}

// Test loading external scripts
console.log('🔄 اختبار تحميل السكريبتات الخارجية...');

// Test simple-realtime.js
const testScript = document.createElement('script');
testScript.src = 'simple-realtime.js';
testScript.onload = () => {
    console.log('✅ تم تحميل simple-realtime.js بنجاح');
    
    // Test creating SimpleRealtime instance
    try {
        if (typeof SimpleRealtime !== 'undefined') {
            const realtime = new SimpleRealtime();
            console.log('✅ تم إنشاء كائن SimpleRealtime بنجاح');
            console.log('📊 حالة النظام:', realtime.getStatus());
        } else {
            console.error('❌ SimpleRealtime غير معرف');
        }
    } catch (error) {
        console.error('❌ خطأ في إنشاء SimpleRealtime:', error);
    }
};
testScript.onerror = () => {
    console.error('❌ فشل في تحميل simple-realtime.js');
};
document.head.appendChild(testScript);

// Test API availability
setTimeout(async () => {
    console.log('🔄 اختبار توفر API...');
    
    try {
        const response = await fetch('realtime-simple.php?action=status');
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ API متاح ويعمل');
            console.log('📊 معلومات النظام:', data);
        } else {
            console.warn('⚠️ API يستجيب لكن مع خطأ:', data);
        }
    } catch (error) {
        console.error('❌ API غير متاح:', error.message);
    }
}, 2000);

// Check for common syntax issues
console.log('🔍 فحص المشاكل الشائعة...');

// Check if there are any global variables that might cause issues
const problematicGlobals = ['websocket', 'pollingInterval', 'reconnectAttempts'];
problematicGlobals.forEach(varName => {
    if (typeof window[varName] !== 'undefined') {
        console.log(`⚠️ متغير عام موجود: ${varName} =`, window[varName]);
    }
});

// Check for required functions
const requiredFunctions = [
    'initializeRealTimeMessaging',
    'loadSimpleRealtimeSystem', 
    'setupRealtimeHandlers',
    'updateRealtimeStatus',
    'sendMessageViaRealtime'
];

requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ دالة مطلوبة موجودة: ${funcName}`);
    } else {
        console.error(`❌ دالة مطلوبة مفقودة: ${funcName}`);
    }
});

console.log('✅ انتهى فحص الأخطاء النحوية');
