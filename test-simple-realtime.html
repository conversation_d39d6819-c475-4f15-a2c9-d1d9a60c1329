<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المبسط للرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام المبسط للرسائل الفورية</h1>
        
        <div id="systemStatus" class="status info">
            🔄 جاري التحقق من النظام...
        </div>
        
        <div class="test-section info">
            <h3>📋 معلومات النظام</h3>
            <p><strong>النظام:</strong> Simple Realtime Messaging</p>
            <p><strong>الطريقة:</strong> HTTP Polling مباشر (بدون WebSocket)</p>
            <p><strong>التحديث:</strong> كل 3 ثوان</p>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <button onclick="testAPI()" id="testApiBtn">🔌 اختبار API</button>
            <button onclick="testInit()" id="testInitBtn">🚀 اختبار التهيئة</button>
            <button onclick="testSend()" id="testSendBtn">📤 اختبار الإرسال</button>
            <button onclick="testPoll()" id="testPollBtn">🔄 اختبار Polling</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>

        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="statusInfo"></div>
        </div>

        <div class="test-section">
            <h3>📋 سجل الاختبارات</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="simple-realtime.js"></script>
    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('systemStatus');
        const statusInfoElement = document.getElementById('statusInfo');
        
        // مستخدم تجريبي
        const testUser = {
            id: 'nurse_687b80778c2d8',
            name: 'مستخدم تجريبي',
            role: 'nurse'
        };
        
        // دالة تسجيل الرسائل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
        }
        
        // تحديث حالة النظام
        function updateSystemStatus(status, message) {
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        // اختبار API
        async function testAPI() {
            log('🔌 اختبار API الأساسي...', 'info');
            
            try {
                // اختبار status
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API يعمل بنجاح', 'success');
                    log(`📊 إحصائيات: ${data.total_users} مستخدم، ${data.recent_messages_24h} رسالة خلال 24 ساعة`, 'info');
                    updateSystemStatus('success', '✅ API متاح ويعمل');
                    return true;
                } else {
                    log('❌ API لا يعمل: ' + JSON.stringify(data), 'error');
                    updateSystemStatus('error', '❌ API لا يعمل');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في API: ' + error.message, 'error');
                updateSystemStatus('error', '❌ خطأ في الاتصال');
                return false;
            }
        }
        
        // اختبار التهيئة
        async function testInit() {
            log('🚀 اختبار تهيئة النظام...', 'info');
            
            try {
                const success = await simpleRealtime.init(testUser);
                
                if (success) {
                    log('✅ تم تهيئة النظام بنجاح', 'success');
                    updateSystemStatus('success', '🟢 النظام نشط ويعمل');
                    
                    // عرض معلومات النظام
                    const status = simpleRealtime.getStatus();
                    statusInfoElement.innerHTML = `
                        <div class="success">
                            <h4>📊 معلومات النظام:</h4>
                            <p>• الحالة: ${status.active ? 'نشط' : 'غير نشط'}</p>
                            <p>• المستخدم: ${status.user?.name}</p>
                            <p>• المستخدمين المتصلين: ${status.online_users_count}</p>
                            <p>• آخر تحديث: ${status.last_poll || 'لم يتم بعد'}</p>
                            <p>• فترة التحديث: ${status.poll_interval}ms</p>
                        </div>
                    `;
                    
                    return true;
                } else {
                    log('❌ فشل في تهيئة النظام', 'error');
                    updateSystemStatus('error', '❌ فشل في التهيئة');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في التهيئة: ' + error.message, 'error');
                updateSystemStatus('error', '❌ خطأ في التهيئة');
                return false;
            }
        }
        
        // اختبار الإرسال
        async function testSend() {
            log('📤 اختبار إرسال رسالة...', 'info');
            
            if (!simpleRealtime.isActive) {
                log('⚠️ النظام غير نشط - يجب تهيئته أولاً', 'warning');
                return;
            }
            
            try {
                const success = await simpleRealtime.sendMessage(
                    'nurse_001', 
                    'رسالة اختبار من النظام المبسط - ' + new Date().toLocaleTimeString(),
                    []
                );
                
                if (success) {
                    log('✅ تم إرسال الرسالة بنجاح', 'success');
                } else {
                    log('❌ فشل في إرسال الرسالة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في الإرسال: ' + error.message, 'error');
            }
        }
        
        // اختبار Polling
        async function testPoll() {
            log('🔄 اختبار Polling يدوياً...', 'info');
            
            try {
                const response = await fetch(`realtime-simple.php?action=poll&user_id=${testUser.id}&last_check=2024-01-01`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Polling يعمل - رسائل جديدة: ${data.new_messages?.length || 0}`, 'success');
                    log(`👥 مستخدمين متصلين: ${data.online_users?.length || 0}`, 'info');
                    
                    if (data.online_users && data.online_users.length > 0) {
                        log('📋 قائمة المستخدمين:', 'info');
                        data.online_users.slice(0, 5).forEach(user => {
                            log(`  - ${user.name} (${user.id})`, 'info');
                        });
                    }
                } else {
                    log('❌ Polling فشل: ' + JSON.stringify(data), 'error');
                }
            } catch (error) {
                log('❌ خطأ في Polling: ' + error.message, 'error');
            }
        }
        
        // إعداد معالجات الأحداث
        function setupEventHandlers() {
            // معالج الرسائل الجديدة
            simpleRealtime.onMessage((message) => {
                log(`📨 رسالة جديدة من ${message.sender_name}: ${message.content}`, 'success');
            });
            
            // معالج تغيير الحالة
            simpleRealtime.onStatusChange((event) => {
                if (event.type === 'status_change') {
                    log(`🔄 تغيير الحالة: ${event.status}`, 'info');
                } else if (Array.isArray(event)) {
                    log(`👥 تحديث المستخدمين المتصلين: ${event.length} مستخدم`, 'info');
                }
            });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', async () => {
            log('🚀 بدء الاختبار التلقائي للنظام المبسط', 'info');
            
            // إعداد معالجات الأحداث
            setupEventHandlers();
            
            // اختبار تدريجي
            const apiWorks = await testAPI();
            
            if (apiWorks) {
                setTimeout(async () => {
                    await testInit();
                }, 2000);
            }
        });
    </script>
</body>
</html>
