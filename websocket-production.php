<?php
/**
 * WebSocket Server للإنتاج - csmanager.online
 * Production WebSocket Server for csmanager.online
 */

require_once 'vendor/autoload.php';
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;

class ProductionMessagingServer implements MessageComponentInterface {
    protected $clients;
    protected $users;
    protected $pdo;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->users = [];
        
        // إعدادات قاعدة البيانات للإنتاج
        try {
            // يجب تحديث هذه الإعدادات حسب خادم الإنتاج
            $host = 'localhost'; // أو عنوان خادم قاعدة البيانات
            $dbname = 'csmanager_db'; // اسم قاعدة البيانات في الإنتاج
            $username = 'csmanager_user'; // مستخدم قاعدة البيانات
            $password = 'your_production_password'; // كلمة مرور الإنتاج
            
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $this->pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            echo "✅ تم الاتصال بقاعدة البيانات بنجاح (الإنتاج)\n";
        } catch (Exception $e) {
            echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "🔗 اتصال جديد: {$conn->resourceId} من " . $conn->remoteAddress . "\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);
        
        if (!$data) {
            echo "❌ رسالة غير صحيحة من {$from->resourceId}\n";
            return;
        }

        switch ($data['type']) {
            case 'auth':
                $this->handleAuth($from, $data);
                break;
                
            case 'message':
                $this->handleMessage($from, $data);
                break;
                
            case 'typing':
                $this->handleTyping($from, $data);
                break;
                
            case 'heartbeat':
                $this->handleHeartbeat($from, $data);
                break;
                
            default:
                echo "❓ نوع رسالة غير معروف: {$data['type']}\n";
        }
    }

    public function onClose(ConnectionInterface $conn) {
        if (isset($this->users[$conn->resourceId])) {
            $user = $this->users[$conn->resourceId];
            unset($this->users[$conn->resourceId]);
            
            $this->broadcastUserStatus($user['id'], 'offline');
            echo "👋 المستخدم {$user['name']} غادر (IP: {$conn->remoteAddress})\n";
        }
        
        $this->clients->detach($conn);
        echo "🔌 انقطع الاتصال: {$conn->resourceId}\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "❌ خطأ في الاتصال {$conn->resourceId}: {$e->getMessage()}\n";
        $conn->close();
    }

    private function handleAuth($conn, $data) {
        $userId = $data['user_id'] ?? null;
        $centerId = $data['center_id'] ?? null;
        
        if (!$userId || !$centerId) {
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'معرف المستخدم والمركز مطلوبان'
            ]));
            return;
        }

        try {
            $stmt = $this->pdo->prepare("SELECT id, name, role FROM users WHERE id = ? AND center_id = ?");
            $stmt->execute([$userId, $centerId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $conn->send(json_encode([
                    'type' => 'error',
                    'message' => 'مستخدم غير صحيح'
                ]));
                return;
            }

            $this->users[$conn->resourceId] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'role' => $user['role'],
                'center_id' => $centerId,
                'last_seen' => time(),
                'ip' => $conn->remoteAddress
            ];

            $conn->send(json_encode([
                'type' => 'auth_success',
                'user' => $user
            ]));

            $this->broadcastUserStatus($user['id'], 'online');
            $this->sendOnlineUsers($conn, $centerId);
            
            echo "✅ تم تسجيل دخول: {$user['name']} من {$conn->remoteAddress}\n";
            
        } catch (Exception $e) {
            echo "❌ خطأ في المصادقة: " . $e->getMessage() . "\n";
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'خطأ في المصادقة'
            ]));
        }
    }

    private function handleMessage($conn, $data) {
        if (!isset($this->users[$conn->resourceId])) {
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'يجب تسجيل الدخول أولاً'
            ]));
            return;
        }

        $sender = $this->users[$conn->resourceId];
        $receiverId = $data['receiver_id'] ?? null;
        $content = $data['content'] ?? '';
        $attachments = $data['attachments'] ?? [];

        if (!$receiverId || empty($content)) {
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'معرف المستقبل والمحتوى مطلوبان'
            ]));
            return;
        }

        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO messages (sender_id, receiver_id, message, attachments) 
                VALUES (?, ?, ?, ?)
            ");
            
            $attachmentsJson = empty($attachments) ? null : json_encode($attachments);
            $stmt->execute([$sender['id'], $receiverId, $content, $attachmentsJson]);
            
            $messageId = $this->pdo->lastInsertId();
            
            $message = [
                'id' => $messageId,
                'sender_id' => $sender['id'],
                'sender_name' => $sender['name'],
                'receiver_id' => $receiverId,
                'content' => $content,
                'attachments' => $attachments,
                'timestamp' => date('Y-m-d H:i:s'),
                'read' => false
            ];

            $conn->send(json_encode([
                'type' => 'message_sent',
                'message' => $message
            ]));

            $this->sendToUser($receiverId, [
                'type' => 'new_message',
                'message' => $message
            ]);

            echo "📨 رسالة من {$sender['name']} إلى {$receiverId} (IP: {$sender['ip']})\n";
            
        } catch (Exception $e) {
            echo "❌ خطأ في إرسال الرسالة: " . $e->getMessage() . "\n";
            $conn->send(json_encode([
                'type' => 'error',
                'message' => 'فشل في إرسال الرسالة'
            ]));
        }
    }

    private function handleTyping($conn, $data) {
        if (!isset($this->users[$conn->resourceId])) {
            return;
        }

        $sender = $this->users[$conn->resourceId];
        $receiverId = $data['receiver_id'] ?? null;
        $isTyping = $data['is_typing'] ?? false;

        if ($receiverId) {
            $this->sendToUser($receiverId, [
                'type' => 'typing',
                'sender_id' => $sender['id'],
                'sender_name' => $sender['name'],
                'is_typing' => $isTyping
            ]);
        }
    }

    private function handleHeartbeat($conn, $data) {
        if (isset($this->users[$conn->resourceId])) {
            $this->users[$conn->resourceId]['last_seen'] = time();
            
            $conn->send(json_encode([
                'type' => 'heartbeat_ack',
                'timestamp' => time()
            ]));
        }
    }

    private function sendToUser($userId, $message) {
        foreach ($this->users as $resourceId => $user) {
            if ($user['id'] === $userId) {
                foreach ($this->clients as $client) {
                    if ($client->resourceId === $resourceId) {
                        $client->send(json_encode($message));
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private function broadcastUserStatus($userId, $status) {
        $message = [
            'type' => 'user_status',
            'user_id' => $userId,
            'status' => $status,
            'timestamp' => time()
        ];

        foreach ($this->clients as $client) {
            if (isset($this->users[$client->resourceId])) {
                $client->send(json_encode($message));
            }
        }
    }

    private function sendOnlineUsers($conn, $centerId) {
        $onlineUsers = [];
        
        foreach ($this->users as $user) {
            if ($user['center_id'] === $centerId) {
                $onlineUsers[] = [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'role' => $user['role'],
                    'status' => 'online'
                ];
            }
        }

        $conn->send(json_encode([
            'type' => 'online_users',
            'users' => $onlineUsers
        ]));
    }
}

// إعدادات الإنتاج
$port = 8080;
$address = '0.0.0.0'; // للاستماع على جميع العناوين

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new ProductionMessagingServer()
        )
    ),
    $port,
    $address
);

echo "🚀 خادم WebSocket للإنتاج يعمل على {$address}:{$port}\n";
echo "🌐 متاح على: wss://www.csmanager.online:{$port}\n";
echo "📡 في انتظار الاتصالات...\n";

$server->run();
