/**
 * سكريبت تشخيص نظام HTTP Polling Fallback
 * يمكن تشغيله في console المتصفح للتحقق من حالة النظام
 */

console.log('🔍 بدء تشخيص نظام الرسائل الفورية...');

// فحص المتغيرات الأساسية
function checkBasicVariables() {
    console.log('\n📋 فحص المتغيرات الأساسية:');
    console.log('- currentUser:', typeof currentUser !== 'undefined' ? currentUser : 'غير معرف');
    console.log('- enableWebSocket:', typeof enableWebSocket !== 'undefined' ? enableWebSocket : 'غير معرف');
    console.log('- reconnectAttempts:', typeof reconnectAttempts !== 'undefined' ? reconnectAttempts : 'غير معرف');
    console.log('- maxReconnectAttempts:', typeof maxReconnectAttempts !== 'undefined' ? maxReconnectAttempts : 'غير معرف');
    console.log('- pollingInterval:', typeof pollingInterval !== 'undefined' ? !!pollingInterval : 'غير معرف');
    console.log('- websocket state:', typeof websocket !== 'undefined' && websocket ? websocket.readyState : 'غير معرف');
}

// فحص حالة WebSocket
function checkWebSocketState() {
    console.log('\n🌐 فحص حالة WebSocket:');
    if (typeof websocket !== 'undefined' && websocket) {
        const states = {
            0: 'CONNECTING',
            1: 'OPEN', 
            2: 'CLOSING',
            3: 'CLOSED'
        };
        console.log('- WebSocket state:', states[websocket.readyState] || 'UNKNOWN');
        console.log('- WebSocket URL:', websocket.url);
    } else {
        console.log('- WebSocket: غير موجود');
    }
}

// فحص HTTP Polling
function checkPollingState() {
    console.log('\n🔄 فحص حالة HTTP Polling:');
    console.log('- Polling active:', typeof pollingInterval !== 'undefined' ? !!pollingInterval : 'غير معرف');
    console.log('- API URL:', typeof window.pollingApiUrl !== 'undefined' ? window.pollingApiUrl : 'غير محدد');
    console.log('- Last poll time:', typeof lastPollTime !== 'undefined' ? lastPollTime : 'غير معرف');
}

// اختبار API مباشر
async function testAPI() {
    console.log('\n🧪 اختبار API مباشر:');
    
    if (typeof currentUser === 'undefined' || !currentUser) {
        console.log('❌ لا يوجد مستخدم متصل - لا يمكن اختبار API');
        return;
    }
    
    try {
        // اختبار websocket-simple.php
        console.log('🔄 اختبار websocket-simple.php...');
        const response1 = await fetch(`websocket-simple.php?action=heartbeat&user_id=${currentUser.id}`);
        const data1 = await response1.json();
        console.log('- websocket-simple.php:', data1.success ? '✅ يعمل' : '❌ لا يعمل', data1);
        
        // اختبار websocket-fallback.php
        console.log('🔄 اختبار websocket-fallback.php...');
        const response2 = await fetch(`websocket-fallback.php?action=heartbeat&user_id=${currentUser.id}`);
        const data2 = await response2.json();
        console.log('- websocket-fallback.php:', data2.success ? '✅ يعمل' : '❌ لا يعمل', data2);
        
    } catch (error) {
        console.log('❌ خطأ في اختبار API:', error.message);
    }
}

// فحص الدوال المطلوبة
function checkRequiredFunctions() {
    console.log('\n🔧 فحص الدوال المطلوبة:');
    const requiredFunctions = [
        'initializeWebSocket',
        'startPolling', 
        'stopPolling',
        'testHttpPolling',
        'showRealtimeStatus'
    ];
    
    requiredFunctions.forEach(funcName => {
        const exists = typeof window[funcName] === 'function';
        console.log(`- ${funcName}:`, exists ? '✅ موجود' : '❌ غير موجود');
    });
}

// تشغيل التشخيص الكامل
async function runDiagnostics() {
    console.log('🔍 تشخيص شامل لنظام الرسائل الفورية');
    console.log('='.repeat(50));
    
    checkBasicVariables();
    checkWebSocketState();
    checkPollingState();
    checkRequiredFunctions();
    await testAPI();
    
    console.log('\n📊 توصيات:');
    
    // تحليل الحالة وإعطاء توصيات
    if (typeof currentUser === 'undefined' || !currentUser) {
        console.log('🔴 يجب تسجيل الدخول أولاً');
        return;
    }
    
    if (typeof pollingInterval !== 'undefined' && pollingInterval) {
        console.log('🟢 HTTP Polling يعمل - النظام جاهز');
    } else if (typeof websocket !== 'undefined' && websocket && websocket.readyState === 1) {
        console.log('🟢 WebSocket يعمل - النظام جاهز');
    } else {
        console.log('🟡 يجب تفعيل HTTP Polling يدوياً');
        console.log('💡 شغل: testHttpPolling()');
    }
}

// تفعيل HTTP Polling يدوياً
function forcePolling() {
    console.log('🔄 تفعيل HTTP Polling يدوياً...');
    
    if (typeof testHttpPolling === 'function') {
        testHttpPolling();
        console.log('✅ تم تشغيل testHttpPolling()');
    } else {
        console.log('❌ دالة testHttpPolling غير متاحة');
        
        // محاولة تفعيل مباشر
        if (typeof startPolling === 'function') {
            startPolling();
            console.log('✅ تم تشغيل startPolling()');
        } else {
            console.log('❌ دالة startPolling غير متاحة');
        }
    }
}

// إضافة الدوال للنافذة العامة
window.runDiagnostics = runDiagnostics;
window.forcePolling = forcePolling;
window.checkBasicVariables = checkBasicVariables;
window.testAPI = testAPI;

// تشغيل التشخيص تلقائياً
console.log('🛠️ أدوات التشخيص جاهزة!');
console.log('📋 الأوامر المتاحة:');
console.log('- runDiagnostics() : تشخيص شامل');
console.log('- forcePolling() : تفعيل HTTP Polling يدوياً');
console.log('- testAPI() : اختبار API');
console.log('- checkBasicVariables() : فحص المتغيرات');

// تشغيل تلقائي بعد ثانيتين
setTimeout(() => {
    console.log('\n🚀 تشغيل التشخيص التلقائي...');
    runDiagnostics();
}, 2000);
