<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الموقع المباشر - نظام الرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .big-status {
            font-size: 2em;
            margin: 20px 0;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 اختبار الموقع المباشر - نظام الرسائل الفورية</h1>
        
        <div id="overallStatus" class="big-status info">
            🔄 جاري الفحص...
        </div>
        
        <div class="test-section info">
            <h3>📋 معلومات الاختبار</h3>
            <p><strong>الهدف:</strong> التحقق من عمل نظام HTTP Polling على الموقع المباشر</p>
            <p><strong>الموقع:</strong> <span id="currentUrl"></span></p>
            <p><strong>التاريخ:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="status-grid">
            <div id="apiStatus" class="status-card info">
                <h4>🔌 حالة API</h4>
                <div id="apiResult">جاري الفحص...</div>
            </div>
            
            <div id="dbStatus" class="status-card info">
                <h4>🗄️ قاعدة البيانات</h4>
                <div id="dbResult">جاري الفحص...</div>
            </div>
            
            <div id="usersStatus" class="status-card info">
                <h4>👥 المستخدمين</h4>
                <div id="usersResult">جاري الفحص...</div>
            </div>
            
            <div id="messagesStatus" class="status-card info">
                <h4>📨 الرسائل</h4>
                <div id="messagesResult">جاري الفحص...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <button onclick="runFullTest()" id="testBtn">🧪 تشغيل اختبار شامل</button>
            <button onclick="testHeartbeat()">💓 اختبار Heartbeat</button>
            <button onclick="testPolling()">🔄 اختبار Polling</button>
            <button onclick="testRealUsers()">👥 اختبار مستخدمين حقيقيين</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>

        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>📋 سجل مفصل</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // تهيئة الصفحة
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentTime').textContent = new Date().toLocaleString('ar');
        
        const logElement = document.getElementById('log');
        const resultsElement = document.getElementById('testResults');
        
        // دالة تسجيل الرسائل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
            resultsElement.innerHTML = '';
        }
        
        // تحديث حالة العنصر
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status-card ${status}`;
            element.querySelector('div').textContent = message;
        }
        
        // تحديث الحالة العامة
        function updateOverallStatus(status, message) {
            const element = document.getElementById('overallStatus');
            element.className = `big-status ${status}`;
            element.textContent = message;
        }
        
        // اختبار Heartbeat
        async function testHeartbeat() {
            log('🔄 اختبار Heartbeat API...', 'info');
            
            try {
                // جرب مع مستخدم تجريبي
                const response = await fetch('websocket-simple.php?action=heartbeat&user_id=test_user');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Heartbeat API يعمل', 'success');
                    updateStatus('apiStatus', 'success', 'API يعمل بنجاح');
                    return true;
                } else {
                    log('❌ Heartbeat فشل: ' + JSON.stringify(data), 'error');
                    updateStatus('apiStatus', 'error', 'API لا يعمل');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في Heartbeat: ' + error.message, 'error');
                updateStatus('apiStatus', 'error', 'خطأ في الاتصال');
                return false;
            }
        }
        
        // اختبار Polling
        async function testPolling() {
            log('🔄 اختبار Polling API...', 'info');
            
            try {
                const lastCheck = new Date(Date.now() - 60000).toISOString();
                const response = await fetch(`websocket-simple.php?action=poll&user_id=test_user&last_check=${encodeURIComponent(lastCheck)}`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Polling يعمل - رسائل: ${data.new_messages?.length || 0}, مستخدمين: ${data.online_users?.length || 0}`, 'success');
                    updateStatus('usersStatus', 'success', `${data.online_users?.length || 0} مستخدم متصل`);
                    updateStatus('messagesStatus', 'success', `${data.new_messages?.length || 0} رسائل جديدة`);
                    return true;
                } else {
                    log('❌ Polling فشل: ' + JSON.stringify(data), 'error');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في Polling: ' + error.message, 'error');
                return false;
            }
        }
        
        // اختبار مستخدمين حقيقيين
        async function testRealUsers() {
            log('🔄 اختبار مع مستخدمين حقيقيين...', 'info');
            
            try {
                // الحصول على قائمة المستخدمين أولاً
                const pollResponse = await fetch('websocket-simple.php?action=poll&user_id=test_user&last_check=2024-01-01');
                const pollData = await pollResponse.json();
                
                if (pollData.success && pollData.online_users && pollData.online_users.length > 0) {
                    const realUser = pollData.online_users[0];
                    log(`👤 اختبار مع مستخدم حقيقي: ${realUser.name} (${realUser.id})`, 'info');
                    
                    // اختبار heartbeat مع مستخدم حقيقي
                    const heartbeatResponse = await fetch(`websocket-simple.php?action=heartbeat&user_id=${realUser.id}`);
                    const heartbeatData = await heartbeatResponse.json();
                    
                    if (heartbeatData.success) {
                        log('✅ اختبار المستخدم الحقيقي نجح', 'success');
                        updateStatus('dbStatus', 'success', 'قاعدة البيانات متصلة');
                        return true;
                    } else {
                        log('❌ اختبار المستخدم الحقيقي فشل: ' + JSON.stringify(heartbeatData), 'error');
                        return false;
                    }
                } else {
                    log('⚠️ لا توجد مستخدمين في قاعدة البيانات', 'warning');
                    updateStatus('dbStatus', 'warning', 'لا توجد مستخدمين');
                    return false;
                }
            } catch (error) {
                log('❌ خطأ في اختبار المستخدمين الحقيقيين: ' + error.message, 'error');
                updateStatus('dbStatus', 'error', 'خطأ في قاعدة البيانات');
                return false;
            }
        }
        
        // اختبار شامل
        async function runFullTest() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 جاري الاختبار...';
            
            clearLog();
            log('🚀 بدء الاختبار الشامل لنظام الرسائل الفورية', 'info');
            log('🌐 الموقع: ' + window.location.href, 'info');
            
            updateOverallStatus('warning', '🔄 جاري الاختبار...');
            
            const results = {
                heartbeat: false,
                polling: false,
                realUsers: false,
                overall: false
            };
            
            // اختبار 1: Heartbeat
            log('\n=== اختبار 1: Heartbeat API ===', 'info');
            results.heartbeat = await testHeartbeat();
            
            // اختبار 2: Polling
            log('\n=== اختبار 2: Polling API ===', 'info');
            results.polling = await testPolling();
            
            // اختبار 3: مستخدمين حقيقيين
            log('\n=== اختبار 3: مستخدمين حقيقيين ===', 'info');
            results.realUsers = await testRealUsers();
            
            // تقييم النتائج
            log('\n=== تقييم النتائج ===', 'info');
            
            if (results.heartbeat && results.polling && results.realUsers) {
                results.overall = true;
                log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام', 'success');
                updateOverallStatus('success', '🎉 النظام يعمل بنجاح!');
                
                resultsElement.innerHTML = `
                    <div class="success">
                        <h4>✅ نتيجة الاختبار: نجح</h4>
                        <p>🟢 نظام الرسائل الفورية جاهز للاستخدام على الموقع المباشر</p>
                        <p>📊 جميع المكونات تعمل بشكل صحيح</p>
                        <p>🚀 يمكن الآن استخدام الرسائل الفورية عبر HTTP Polling</p>
                    </div>
                `;
            } else if (results.heartbeat && results.polling) {
                log('⚠️ النظام يعمل جزئياً - قد تحتاج لإعداد قاعدة البيانات', 'warning');
                updateOverallStatus('warning', '⚠️ يعمل جزئياً');
                
                resultsElement.innerHTML = `
                    <div class="warning">
                        <h4>⚠️ نتيجة الاختبار: يعمل جزئياً</h4>
                        <p>🟡 API يعمل لكن قد تحتاج لإعداد قاعدة البيانات</p>
                        <p>🔧 تحقق من إعدادات قاعدة البيانات في websocket-simple.php</p>
                    </div>
                `;
            } else {
                log('❌ النظام لا يعمل - تحقق من الإعدادات', 'error');
                updateOverallStatus('error', '❌ النظام لا يعمل');
                
                resultsElement.innerHTML = `
                    <div class="error">
                        <h4>❌ نتيجة الاختبار: فشل</h4>
                        <p>🔴 النظام لا يعمل حالياً</p>
                        <p>🔧 تحقق من:</p>
                        <ul>
                            <li>وجود ملف websocket-simple.php</li>
                            <li>إعدادات قاعدة البيانات</li>
                            <li>صلاحيات الملفات</li>
                        </ul>
                    </div>
                `;
            }
            
            log('\n📊 ملخص النتائج:', 'info');
            log(`- Heartbeat: ${results.heartbeat ? '✅' : '❌'}`, 'info');
            log(`- Polling: ${results.polling ? '✅' : '❌'}`, 'info');
            log(`- Real Users: ${results.realUsers ? '✅' : '❌'}`, 'info');
            log(`- Overall: ${results.overall ? '✅' : '❌'}`, 'info');
            
            testBtn.disabled = false;
            testBtn.textContent = '🧪 تشغيل اختبار شامل';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
