<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            font-size: 1.2em;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 1em;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
        .test-info {
            background: #e3f2fd;
            border: 1px solid #90caf9;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ اختبار سريع للرسائل الفورية</h1>
        
        <div class="test-info">
            <h3>📋 هذا الاختبار سيقوم بـ:</h3>
            <ul>
                <li>✅ إرسال رسالة بين مستخدمين حقيقيين</li>
                <li>✅ التحقق من استقبال الرسالة فوراً</li>
                <li>✅ اختبار النظام الفوري الكامل</li>
                <li>✅ عرض نتيجة واضحة: يعمل أم لا</li>
            </ul>
        </div>
        
        <div id="status" class="status info">اضغط "بدء الاختبار" للتحقق من الرسائل الفورية</div>
        
        <div style="text-align: center;">
            <button onclick="runQuickTest()" id="testBtn">🚀 بدء الاختبار السريع</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div id="log" class="log">جاري التحضير للاختبار...</div>
    </div>

    <script src="simple-realtime.js"></script>
    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        const testBtn = document.getElementById('testBtn');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry.trim());
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function updateStatus(type, message) {
            statusElement.className = `status ${type}`;
            statusElement.textContent = message;
        }
        
        async function runQuickTest() {
            testBtn.disabled = true;
            testBtn.textContent = '🔄 جاري الاختبار...';
            
            clearLog();
            log('🚀 بدء الاختبار السريع للرسائل الفورية');
            updateStatus('info', '🔄 جاري تشغيل الاختبار...');
            
            try {
                // الخطوة 1: جلب المستخدمين
                log('👥 الخطوة 1: جلب المستخدمين المتاحين...');
                const usersResponse = await fetch('realtime-simple.php?action=poll&user_id=test&last_check=2024-01-01');
                const usersData = await usersResponse.json();
                
                if (!usersData.success || !usersData.online_users || usersData.online_users.length < 2) {
                    throw new Error('لا يوجد مستخدمين كافيين في قاعدة البيانات');
                }
                
                const sender = usersData.online_users[0];
                const receiver = usersData.online_users[1];
                
                log(`✅ تم العثور على ${usersData.online_users.length} مستخدم`);
                log(`👤 المرسل: ${sender.name} (${sender.id})`);
                log(`👤 المستقبل: ${receiver.name} (${receiver.id})`);
                
                // الخطوة 2: إرسال رسالة
                log('📤 الخطوة 2: إرسال رسالة اختبار...');
                const testMessage = `رسالة اختبار سريعة - ${new Date().toLocaleTimeString()}`;
                
                const sendResponse = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: sender.id,
                        receiver_id: receiver.id,
                        content: testMessage,
                        attachments: []
                    })
                });
                
                const sendData = await sendResponse.json();
                
                if (!sendData.success) {
                    throw new Error(`فشل في الإرسال: ${sendData.error}`);
                }
                
                log(`✅ تم إرسال الرسالة بنجاح - ID: ${sendData.message_id}`);
                log(`⏰ وقت الإرسال: ${sendData.timestamp}`);
                
                // الخطوة 3: فحص الاستقبال الفوري
                log('📬 الخطوة 3: فحص الاستقبال الفوري...');
                
                // انتظار ثانيتين ثم فحص
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // استخدام وقت قبل الإرسال بدقيقة
                const beforeTime = new Date(new Date(sendData.timestamp).getTime() - 60000);
                const beforeTimeStr = beforeTime.getFullYear() + '-' +
                    String(beforeTime.getMonth() + 1).padStart(2, '0') + '-' +
                    String(beforeTime.getDate()).padStart(2, '0') + ' ' +
                    String(beforeTime.getHours()).padStart(2, '0') + ':' +
                    String(beforeTime.getMinutes()).padStart(2, '0') + ':' +
                    String(beforeTime.getSeconds()).padStart(2, '0');
                
                log(`🔍 فحص الرسائل منذ: ${beforeTimeStr}`);
                
                const pollResponse = await fetch(`realtime-simple.php?action=poll&user_id=${receiver.id}&last_check=${encodeURIComponent(beforeTimeStr)}`);
                const pollData = await pollResponse.json();
                
                if (!pollData.success) {
                    throw new Error(`فشل في polling: ${pollData.error}`);
                }
                
                log(`📊 نتائج polling:`);
                log(`  - رسائل جديدة: ${pollData.new_messages?.length || 0}`);
                log(`  - جميع رسائل المستخدم: ${pollData.debug?.all_user_messages_count || 0}`);
                
                // عرض معلومات التشخيص
                if (pollData.debug && pollData.debug.time_comparison_sample) {
                    const comp = pollData.debug.time_comparison_sample;
                    log(`🔍 مقارنة الأوقات:`);
                    log(`  - وقت آخر رسالة: ${comp.message_time} (${comp.message_timestamp})`);
                    log(`  - وقت آخر فحص: ${pollData.debug.last_check} (${comp.last_check_timestamp})`);
                    log(`  - الرسالة أحدث؟ ${comp.is_newer ? 'نعم' : 'لا'}`);
                }
                
                // البحث عن رسالتنا
                const ourMessage = pollData.new_messages?.find(msg => msg.id == sendData.message_id);
                
                if (ourMessage) {
                    log('🎉 نجح! تم العثور على الرسالة');
                    log(`📨 الرسالة: ${ourMessage.message}`);
                    log(`👤 من: ${ourMessage.sender_name}`);
                    log(`⏰ الوقت: ${ourMessage.sent_at}`);
                    
                    // الخطوة 4: اختبار النظام الفوري الكامل
                    log('🔄 الخطوة 4: اختبار النظام الفوري الكامل...');
                    await testFullRealtimeSystem(sender, receiver);
                    
                } else {
                    log('❌ فشل! لم يتم العثور على الرسالة');
                    
                    // عرض جميع الرسائل للتشخيص
                    if (pollData.debug?.all_messages_for_user) {
                        log('📋 جميع رسائل المستخدم:');
                        pollData.debug.all_messages_for_user.forEach(msg => {
                            log(`  - ID: ${msg.id}, الوقت: ${msg.sent_at}, النص: ${msg.message}`);
                        });
                    }
                    
                    updateStatus('error', '❌ الرسائل الفورية لا تعمل');
                    testBtn.disabled = false;
                    testBtn.textContent = '🚀 بدء الاختبار السريع';
                    return;
                }
                
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`);
                updateStatus('error', `❌ خطأ: ${error.message}`);
                testBtn.disabled = false;
                testBtn.textContent = '🚀 بدء الاختبار السريع';
            }
        }
        
        async function testFullRealtimeSystem(sender, receiver) {
            try {
                log('🔄 اختبار النظام الفوري الكامل...');
                
                // إنشاء نظام فوري للمستقبل
                const realtime = new SimpleRealtime();
                let messageReceived = false;
                
                // إعداد معالج الرسائل
                realtime.onMessage((message) => {
                    if (message.type === 'received') {
                        log(`📨 تم استقبال رسالة فورية: ${message.content}`);
                        messageReceived = true;
                    }
                });
                
                // تهيئة النظام
                const success = await realtime.init({
                    id: receiver.id,
                    name: receiver.name,
                    role: receiver.role
                });
                
                if (!success) {
                    throw new Error('فشل في تهيئة النظام الفوري');
                }
                
                log('✅ تم تهيئة النظام الفوري بنجاح');
                
                // انتظار 5 ثوان للـ polling
                log('⏳ انتظار 5 ثوان للـ polling...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // إرسال رسالة أثناء تشغيل النظام
                log('📤 إرسال رسالة أثناء تشغيل النظام الفوري...');
                const realtimeMessage = `رسالة فورية - ${new Date().toLocaleTimeString()}`;
                
                const sendResponse = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: sender.id,
                        receiver_id: receiver.id,
                        content: realtimeMessage,
                        attachments: []
                    })
                });
                
                const sendData = await sendResponse.json();
                if (sendData.success) {
                    log(`✅ تم إرسال رسالة فورية - ID: ${sendData.message_id}`);
                }
                
                // انتظار 10 ثوان للتحقق من الاستقبال
                log('⏳ انتظار 10 ثوان للتحقق من الاستقبال الفوري...');
                await new Promise(resolve => setTimeout(resolve, 10000));
                
                // إيقاف النظام
                realtime.stop();
                
                if (messageReceived) {
                    log('🎉 ممتاز! النظام الفوري يعمل بنجاح');
                    updateStatus('success', '🎉 الرسائل الفورية تعمل بنجاح!');
                } else {
                    log('⚠️ النظام الأساسي يعمل لكن لم يتم استقبال رسائل أثناء الاختبار');
                    updateStatus('warning', '⚠️ النظام يعمل جزئياً - قد تحتاج لمزيد من الاختبار');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار النظام الفوري: ${error.message}`);
                updateStatus('error', '❌ خطأ في النظام الفوري');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = '🚀 بدء الاختبار السريع';
        }
        
        // تهيئة الصفحة
        window.addEventListener('load', () => {
            log('⚡ مرحباً بك في الاختبار السريع للرسائل الفورية');
            log('💡 هذا الاختبار سيتحقق من عمل النظام خلال دقيقة واحدة');
            log('🔧 تم تطبيق الإصلاحات الجديدة لحل مشكلة "لم يتم الاستقبال الفوري"');
            log('');
            log('📋 اضغط "بدء الاختبار السريع" للبدء...');
        });
    </script>
</body>
</html>
