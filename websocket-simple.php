<?php
/**
 * WebSocket Fallback - نسخة مبسطة وموثوقة
 * تعمل مع أي هيكل قاعدة بيانات
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'j5aKN6lz5bsujTcWaYAd';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed', 'details' => $e->getMessage()]);
    exit;
}

// تحديد العملية
$requestMethod = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// للطلبات POST، قراءة البيانات من JSON
if ($requestMethod === 'POST') {
    $postData = json_decode(file_get_contents('php://input'), true);
    if ($postData && isset($postData['action'])) {
        $action = $postData['action'];
    }
}

$userId = $_GET['user_id'] ?? ($postData['sender_id'] ?? '');

// تسجيل للتطوير
error_log("Simple WebSocket API: action=$action, method=$requestMethod, userId=$userId");

switch ($action) {
    case 'heartbeat':
        handleHeartbeat($pdo, $userId);
        break;
        
    case 'poll':
        handlePoll($pdo, $userId);
        break;
        
    case 'send':
        handleSend($pdo, $postData ?? []);
        break;
        
    default:
        echo json_encode([
            'error' => 'Invalid action',
            'received' => $action,
            'available' => ['heartbeat', 'poll', 'send']
        ]);
}

function handleHeartbeat($pdo, $userId) {
    if (!$userId) {
        echo json_encode(['error' => 'User ID required']);
        return;
    }

    try {
        // فحص بسيط للمستخدم
        $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ? LIMIT 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if ($user) {
            echo json_encode([
                'success' => true,
                'timestamp' => time(),
                'user' => $user
            ]);
        } else {
            // للمستخدمين التجريبيين، أرجع نجاح مع بيانات وهمية
            if (strpos($userId, 'test_') === 0) {
                echo json_encode([
                    'success' => true,
                    'timestamp' => time(),
                    'user' => [
                        'id' => $userId,
                        'name' => 'مستخدم تجريبي'
                    ],
                    'note' => 'Test user - not in database'
                ]);
            } else {
                echo json_encode(['error' => 'User not found']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['error' => 'Heartbeat failed: ' . $e->getMessage()]);
    }
}

function handlePoll($pdo, $userId) {
    if (!$userId) {
        echo json_encode(['error' => 'User ID required']);
        return;
    }
    
    $lastCheck = $_GET['last_check'] ?? date('Y-m-d H:i:s', time() - 30);
    
    try {
        // فحص الرسائل الجديدة
        $stmt = $pdo->prepare("
            SELECT m.*, u.name as sender_name 
            FROM messages m 
            LEFT JOIN users u ON m.sender_id = u.id 
            WHERE m.receiver_id = ? AND m.sent_at > ? 
            ORDER BY m.sent_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId, $lastCheck]);
        $newMessages = $stmt->fetchAll();
        
        // قائمة مستخدمين مبسطة (جميع المستخدمين النشطين)
        $stmt = $pdo->prepare("
            SELECT id, name, 'online' as status 
            FROM users 
            WHERE is_active = 1 AND id != ? 
            LIMIT 20
        ");
        $stmt->execute([$userId]);
        $onlineUsers = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'new_messages' => $newMessages,
            'online_users' => $onlineUsers,
            'timestamp' => date('Y-m-d H:i:s'),
            'debug' => [
                'user_id' => $userId,
                'last_check' => $lastCheck,
                'messages_count' => count($newMessages)
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Poll failed: ' . $e->getMessage()]);
    }
}

function handleSend($pdo, $data) {
    $senderId = $data['sender_id'] ?? '';
    $receiverId = $data['receiver_id'] ?? '';
    $content = $data['content'] ?? '';
    $attachments = $data['attachments'] ?? [];

    if (!$senderId || !$receiverId || !$content) {
        echo json_encode(['error' => 'Missing required fields: sender_id, receiver_id, content']);
        return;
    }

    // للمستخدمين التجريبيين، أرجع نجاح وهمي
    if (strpos($senderId, 'test_') === 0 || strpos($receiverId, 'test_') === 0) {
        echo json_encode([
            'success' => true,
            'message_id' => rand(1000, 9999),
            'timestamp' => date('Y-m-d H:i:s'),
            'note' => 'Test message - not saved to database'
        ]);
        return;
    }

    try {
        // التحقق من وجود المرسل والمستقبل
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE id IN (?, ?) AND is_active = 1");
        $stmt->execute([$senderId, $receiverId]);
        $userCount = $stmt->fetch()['count'];

        if ($userCount < 2) {
            echo json_encode(['error' => 'Sender or receiver not found or inactive']);
            return;
        }

        $stmt = $pdo->prepare("
            INSERT INTO messages (sender_id, receiver_id, message, attachments)
            VALUES (?, ?, ?, ?)
        ");

        $attachmentsJson = empty($attachments) ? null : json_encode($attachments);
        $stmt->execute([$senderId, $receiverId, $content, $attachmentsJson]);

        $messageId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message_id' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        echo json_encode(['error' => 'Send failed: ' . $e->getMessage()]);
    }
}
?>
