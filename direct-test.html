<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مباشر للرسائل الفورية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار مباشر للرسائل الفورية</h1>
        
        <div id="status" class="status info">جاري التحضير...</div>
        
        <div>
            <button onclick="sendMessage()">📤 إرسال رسالة اختبار</button>
            <button onclick="checkMessages()">📬 فحص الرسائل</button>
            <button onclick="startPolling()">🔄 بدء Polling</button>
            <button onclick="stopPolling()">⏹️ إيقاف Polling</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        let pollingInterval = null;
        let lastPollTime = null;
        
        // المستخدمين للاختبار
        const user1 = 'nurse_687b80778c2d8';
        const user2 = 'nurse_001';
        
        // دالة تسجيل الرسائل
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry.trim());
        }
        
        // مسح السجل
        function clearLog() {
            logElement.textContent = '';
        }
        
        // تحديث الحالة
        function updateStatus(status, message) {
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }
        
        // إرسال رسالة اختبار
        async function sendMessage() {
            log('📤 إرسال رسالة اختبار...');
            updateStatus('info', 'جاري الإرسال...');
            
            const content = 'رسالة اختبار مباشرة - ' + new Date().toLocaleTimeString();
            
            try {
                const response = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: user1,
                        receiver_id: user2,
                        content: content,
                        attachments: []
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ تم إرسال الرسالة بنجاح - ID: ${data.message_id}`);
                    log(`📝 المحتوى: ${content}`);
                    log(`👤 من: ${user1} إلى: ${user2}`);
                    updateStatus('success', `تم الإرسال - ID: ${data.message_id}`);
                    
                    // حفظ وقت الإرسال
                    lastPollTime = data.timestamp;
                } else {
                    log('❌ فشل في الإرسال: ' + data.error);
                    updateStatus('error', 'فشل في الإرسال');
                }
            } catch (error) {
                log('❌ خطأ في الإرسال: ' + error.message);
                updateStatus('error', 'خطأ في الإرسال');
            }
        }
        
        // فحص الرسائل
        async function checkMessages() {
            log('📬 فحص الرسائل الجديدة...');
            
            const checkTime = lastPollTime || '2024-01-01 00:00:00';
            
            try {
                const response = await fetch(`realtime-simple.php?action=poll&user_id=${user2}&last_check=${encodeURIComponent(checkTime)}`);
                const data = await response.json();
                
                if (data.success) {
                    log(`📊 نتائج الفحص:`);
                    log(`  - رسائل جديدة: ${data.new_messages?.length || 0}`);
                    log(`  - رسائل مرسلة: ${data.sent_messages?.length || 0}`);
                    log(`  - مستخدمين متصلين: ${data.online_users?.length || 0}`);
                    
                    if (data.debug) {
                        log(`🔍 معلومات التشخيص:`);
                        log(`  - المستخدم: ${data.debug.user_id}`);
                        log(`  - آخر فحص: ${data.debug.last_check}`);
                        log(`  - الوقت الحالي: ${data.debug.current_time}`);
                    }
                    
                    if (data.new_messages && data.new_messages.length > 0) {
                        log(`📨 الرسائل الجديدة:`);
                        data.new_messages.forEach(msg => {
                            log(`  - ID: ${msg.id}, من: ${msg.sender_name}, النص: ${msg.message}`);
                        });
                        updateStatus('success', `تم العثور على ${data.new_messages.length} رسالة`);
                    } else {
                        log('📭 لا توجد رسائل جديدة');
                        updateStatus('info', 'لا توجد رسائل جديدة');
                    }
                    
                    // تحديث وقت آخر فحص
                    lastPollTime = data.timestamp;
                } else {
                    log('❌ فشل في فحص الرسائل: ' + data.error);
                    updateStatus('error', 'فشل في الفحص');
                }
            } catch (error) {
                log('❌ خطأ في فحص الرسائل: ' + error.message);
                updateStatus('error', 'خطأ في الفحص');
            }
        }
        
        // بدء Polling التلقائي
        function startPolling() {
            if (pollingInterval) {
                log('⚠️ Polling يعمل بالفعل');
                return;
            }
            
            log('🔄 بدء Polling التلقائي...');
            updateStatus('info', 'Polling نشط');
            
            pollingInterval = setInterval(async () => {
                log('🔄 Polling تلقائي...');
                await checkMessages();
            }, 3000);
        }
        
        // إيقاف Polling
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                log('⏹️ تم إيقاف Polling');
                updateStatus('info', 'Polling متوقف');
            } else {
                log('⚠️ Polling غير نشط');
            }
        }
        
        // اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', async () => {
            log('🚀 بدء الاختبار المباشر للرسائل الفورية');
            
            // اختبار API أولاً
            try {
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API يعمل بنجاح');
                    updateStatus('success', 'API متاح');
                } else {
                    log('❌ API لا يعمل');
                    updateStatus('error', 'API لا يعمل');
                }
            } catch (error) {
                log('❌ خطأ في API: ' + error.message);
                updateStatus('error', 'خطأ في API');
            }
        });
        
        // إيقاف Polling عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            stopPolling();
        });
    </script>
</body>
</html>
