<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص الأخطاء النحوية</title>
</head>
<body>
    <h1>فحص الأخطاء النحوية</h1>
    <div id="result"></div>

    <script>
        console.log('🔍 بدء فحص الأخطاء النحوية...');
        
        // اختبار تحميل النظام المبسط
        try {
            const script = document.createElement('script');
            script.src = 'simple-realtime.js';
            script.onload = () => {
                console.log('✅ تم تحميل simple-realtime.js بنجاح');
                document.getElementById('result').innerHTML += '<p>✅ simple-realtime.js: تم التحميل بنجاح</p>';
                
                // اختبار إنشاء كائن
                try {
                    const realtime = new SimpleRealtime();
                    console.log('✅ تم إنشاء كائن SimpleRealtime بنجاح');
                    document.getElementById('result').innerHTML += '<p>✅ SimpleRealtime: تم الإنشاء بنجاح</p>';
                } catch (error) {
                    console.error('❌ خطأ في إنشاء SimpleRealtime:', error);
                    document.getElementById('result').innerHTML += '<p>❌ SimpleRealtime: ' + error.message + '</p>';
                }
            };
            script.onerror = () => {
                console.error('❌ فشل في تحميل simple-realtime.js');
                document.getElementById('result').innerHTML += '<p>❌ simple-realtime.js: فشل في التحميل</p>';
            };
            document.head.appendChild(script);
        } catch (error) {
            console.error('❌ خطأ في تحميل السكريبت:', error);
            document.getElementById('result').innerHTML += '<p>❌ خطأ عام: ' + error.message + '</p>';
        }

        // اختبار API
        setTimeout(async () => {
            try {
                console.log('🔍 اختبار API...');
                const response = await fetch('realtime-simple.php?action=status');
                const data = await response.json();
                
                if (data.success) {
                    console.log('✅ API يعمل بنجاح');
                    document.getElementById('result').innerHTML += '<p>✅ API: يعمل بنجاح</p>';
                } else {
                    console.log('⚠️ API يستجيب لكن مع خطأ:', data);
                    document.getElementById('result').innerHTML += '<p>⚠️ API: ' + JSON.stringify(data) + '</p>';
                }
            } catch (error) {
                console.error('❌ خطأ في API:', error);
                document.getElementById('result').innerHTML += '<p>❌ API: ' + error.message + '</p>';
            }
        }, 2000);
    </script>
</body>
</html>
