<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Simple API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار Simple WebSocket API</h1>
    
    <div class="test">
        <h3>اختبار Heartbeat</h3>
        <button onclick="testHeartbeat()">اختبار</button>
        <div id="heartbeat-result"></div>
    </div>
    
    <div class="test">
        <h3>اختبار Poll</h3>
        <button onclick="testPoll()">اختبار</button>
        <div id="poll-result"></div>
    </div>
    
    <div class="test">
        <h3>اختبار Send</h3>
        <button onclick="testSend()">اختبار</button>
        <div id="send-result"></div>
    </div>

    <script>
        const testUserId = 'test_user_' + Date.now();
        
        async function testHeartbeat() {
            const resultDiv = document.getElementById('heartbeat-result');
            resultDiv.innerHTML = '🔄 جاري الاختبار...';
            
            try {
                const response = await fetch(`websocket-simple.php?action=heartbeat&user_id=${testUserId}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                resultDiv.className = data.success ? 'success' : 'error';
            } catch (error) {
                resultDiv.innerHTML = `<pre>خطأ: ${error.message}</pre>`;
                resultDiv.className = 'error';
            }
        }
        
        async function testPoll() {
            const resultDiv = document.getElementById('poll-result');
            resultDiv.innerHTML = '🔄 جاري الاختبار...';
            
            try {
                const lastCheck = new Date(Date.now() - 60000).toISOString();
                const response = await fetch(`websocket-simple.php?action=poll&user_id=${testUserId}&last_check=${encodeURIComponent(lastCheck)}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                resultDiv.className = data.success ? 'success' : 'error';
            } catch (error) {
                resultDiv.innerHTML = `<pre>خطأ: ${error.message}</pre>`;
                resultDiv.className = 'error';
            }
        }
        
        async function testSend() {
            const resultDiv = document.getElementById('send-result');
            resultDiv.innerHTML = '🔄 جاري الاختبار...';
            
            try {
                const messageData = {
                    action: 'send',
                    sender_id: testUserId,
                    receiver_id: 'test_receiver',
                    content: 'رسالة اختبار من Simple API - ' + new Date().toLocaleTimeString(),
                    attachments: []
                };
                
                const response = await fetch('websocket-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(messageData)
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                resultDiv.className = data.success ? 'success' : 'error';
            } catch (error) {
                resultDiv.innerHTML = `<pre>خطأ: ${error.message}</pre>`;
                resultDiv.className = 'error';
            }
        }
        
        // اختبار تلقائي
        window.addEventListener('load', () => {
            setTimeout(testHeartbeat, 1000);
            setTimeout(testPoll, 2000);
        });
    </script>
</body>
</html>
