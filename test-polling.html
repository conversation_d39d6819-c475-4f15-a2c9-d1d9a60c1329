<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار HTTP Polling</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام HTTP Polling للرسائل الفورية</h1>
        
        <div class="test-section info">
            <h3>معلومات الاختبار</h3>
            <p>هذه الصفحة تختبر نظام HTTP Polling المستخدم كبديل لـ WebSocket</p>
            <p><strong>الهدف:</strong> التأكد من عمل الرسائل الفورية على الموقع المباشر</p>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <button onclick="testHeartbeat()">اختبار Heartbeat</button>
            <button onclick="testPolling()">اختبار Polling</button>
            <button onclick="testSendMessage()">اختبار إرسال رسالة</button>
            <button onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="status">جاري التحقق...</div>
        </div>

        <div class="test-section">
            <h3>📋 سجل الاختبارات</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        const testUserId = 'user123'; // معرف مستخدم تجريبي
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');

        // دالة تسجيل الرسائل
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const div = document.createElement('div');
            div.textContent = logEntry;
            div.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            
            logElement.appendChild(div);
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        // مسح السجل
        function clearLog() {
            logElement.innerHTML = '';
        }

        // اختبار Heartbeat
        async function testHeartbeat() {
            log('🔄 اختبار Heartbeat...', 'info');
            
            try {
                const response = await fetch(`websocket-fallback.php?action=heartbeat&user_id=${testUserId}`);
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Heartbeat نجح', 'success');
                    updateStatus('Heartbeat يعمل', 'success');
                } else {
                    log('❌ Heartbeat فشل: ' + JSON.stringify(data), 'error');
                    updateStatus('Heartbeat فشل', 'error');
                }
            } catch (error) {
                log('❌ خطأ في Heartbeat: ' + error.message, 'error');
                updateStatus('خطأ في الاتصال', 'error');
            }
        }

        // اختبار Polling
        async function testPolling() {
            log('🔄 اختبار Polling...', 'info');
            
            try {
                const lastCheck = new Date(Date.now() - 60000).toISOString(); // آخر دقيقة
                const response = await fetch(`websocket-fallback.php?action=poll&user_id=${testUserId}&last_check=${encodeURIComponent(lastCheck)}`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Polling نجح - رسائل جديدة: ${data.new_messages?.length || 0}, مستخدمين متصلين: ${data.online_users?.length || 0}`, 'success');
                    updateStatus('Polling يعمل', 'success');
                    
                    if (data.new_messages && data.new_messages.length > 0) {
                        log('📨 رسائل جديدة: ' + JSON.stringify(data.new_messages, null, 2), 'info');
                    }
                } else {
                    log('❌ Polling فشل: ' + JSON.stringify(data), 'error');
                    updateStatus('Polling فشل', 'error');
                }
            } catch (error) {
                log('❌ خطأ في Polling: ' + error.message, 'error');
                updateStatus('خطأ في الاتصال', 'error');
            }
        }

        // اختبار إرسال رسالة
        async function testSendMessage() {
            log('🔄 اختبار إرسال رسالة...', 'info');
            
            try {
                const messageData = {
                    action: 'send',
                    sender_id: testUserId,
                    receiver_id: 'user456', // معرف مستقبل تجريبي
                    content: 'رسالة اختبار من HTTP Polling - ' + new Date().toLocaleTimeString(),
                    attachments: []
                };
                
                const response = await fetch('websocket-fallback.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(messageData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ إرسال الرسالة نجح - معرف الرسالة: ${data.message_id}`, 'success');
                    updateStatus('إرسال الرسائل يعمل', 'success');
                } else {
                    log('❌ إرسال الرسالة فشل: ' + JSON.stringify(data), 'error');
                    updateStatus('إرسال الرسائل فشل', 'error');
                }
            } catch (error) {
                log('❌ خطأ في إرسال الرسالة: ' + error.message, 'error');
                updateStatus('خطأ في الاتصال', 'error');
            }
        }

        // تحديث حالة النظام
        function updateStatus(message, type) {
            statusElement.textContent = message;
            statusElement.className = type;
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 بدء اختبار نظام HTTP Polling', 'info');
            
            // اختبار تلقائي
            setTimeout(testHeartbeat, 1000);
            setTimeout(testPolling, 2000);
        });

        // اختبار دوري كل 10 ثوان
        setInterval(() => {
            log('🔄 اختبار دوري...', 'info');
            testPolling();
        }, 10000);
    </script>
</body>
</html>
