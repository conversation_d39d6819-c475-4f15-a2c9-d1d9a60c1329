<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص المستخدمين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .user-list {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .user-item {
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .user-item.sender { border-color: #007bff; background: #e3f2fd; }
        .user-item.receiver { border-color: #28a745; background: #d4edda; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 فحص المستخدمين في قاعدة البيانات</h1>
        
        <button onclick="loadUsers()">تحميل قائمة المستخدمين</button>
        <button onclick="testWithRealUsers()">اختبار مع مستخدمين حقيقيين</button>
        
        <div id="userList" class="user-list">
            <p>اضغط "تحميل قائمة المستخدمين" لعرض المستخدمين المتاحين</p>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        const userListElement = document.getElementById('userList');
        let availableUsers = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry.trim());
        }
        
        async function loadUsers() {
            log('👥 تحميل قائمة المستخدمين...');
            
            try {
                const response = await fetch('realtime-simple.php?action=poll&user_id=test&last_check=2024-01-01');
                const data = await response.json();
                
                if (data.success && data.online_users) {
                    availableUsers = data.online_users;
                    log(`✅ تم تحميل ${availableUsers.length} مستخدم`);
                    
                    // عرض المستخدمين
                    userListElement.innerHTML = '';
                    
                    availableUsers.forEach(user => {
                        const userDiv = document.createElement('div');
                        userDiv.className = 'user-item';
                        
                        // تمييز المستخدمين المستخدمين في الاختبار
                        if (user.id === 'nurse_687b80778c2d8') {
                            userDiv.className += ' sender';
                        } else if (user.id === 'nurse_001') {
                            userDiv.className += ' receiver';
                        }
                        
                        userDiv.innerHTML = `
                            <strong>${user.name}</strong><br>
                            <small>ID: ${user.id} | الدور: ${user.role} | الحالة: ${user.status}</small>
                        `;
                        
                        userListElement.appendChild(userDiv);
                        
                        log(`👤 ${user.name} (${user.id}) - ${user.role}`);
                    });
                    
                    // فحص المستخدمين المطلوبين
                    const sender = availableUsers.find(u => u.id === 'nurse_687b80778c2d8');
                    const receiver = availableUsers.find(u => u.id === 'nurse_001');
                    
                    log('🔍 فحص المستخدمين المطلوبين:');
                    log(`  - المرسل (nurse_687b80778c2d8): ${sender ? '✅ موجود' : '❌ غير موجود'}`);
                    log(`  - المستقبل (nurse_001): ${receiver ? '✅ موجود' : '❌ غير موجود'}`);
                    
                    if (!receiver) {
                        log('⚠️ المستقبل غير موجود! هذا قد يكون سبب المشكلة');
                        log('💡 جرب استخدام مستخدم آخر كمستقبل');
                    }
                    
                } else {
                    throw new Error('فشل في تحميل المستخدمين');
                }
            } catch (error) {
                log(`❌ خطأ في تحميل المستخدمين: ${error.message}`);
            }
        }
        
        async function testWithRealUsers() {
            if (availableUsers.length < 2) {
                log('❌ يجب تحميل المستخدمين أولاً');
                return;
            }
            
            log('🧪 اختبار مع مستخدمين حقيقيين...');
            
            // استخدام أول مستخدمين متاحين
            const sender = availableUsers[0];
            const receiver = availableUsers[1];
            
            log(`👤 المرسل: ${sender.name} (${sender.id})`);
            log(`👤 المستقبل: ${receiver.name} (${receiver.id})`);
            
            try {
                // إرسال رسالة
                const content = `رسالة اختبار حقيقية - ${new Date().toLocaleTimeString()}`;
                
                const sendResponse = await fetch('realtime-simple.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'send',
                        sender_id: sender.id,
                        receiver_id: receiver.id,
                        content: content,
                        attachments: []
                    })
                });
                
                const sendData = await sendResponse.json();
                
                if (sendData.success) {
                    log(`✅ تم إرسال الرسالة - ID: ${sendData.message_id}`);
                    
                    // انتظار ثانيتين ثم فحص الاستقبال
                    setTimeout(async () => {
                        try {
                            const pollResponse = await fetch(`realtime-simple.php?action=poll&user_id=${receiver.id}&last_check=${encodeURIComponent(sendData.timestamp)}`);
                            const pollData = await pollResponse.json();
                            
                            if (pollData.success) {
                                const newMessages = pollData.new_messages || [];
                                log(`📬 فحص الاستقبال: ${newMessages.length} رسالة جديدة`);
                                
                                const ourMessage = newMessages.find(msg => msg.id == sendData.message_id);
                                
                                if (ourMessage) {
                                    log('🎉 نجح! تم العثور على الرسالة');
                                    log(`📨 الرسالة: ${ourMessage.message}`);
                                } else {
                                    log('❌ فشل! لم يتم العثور على الرسالة');
                                    
                                    if (pollData.debug) {
                                        log('🔍 معلومات التشخيص:');
                                        log(`  - المستخدم: ${pollData.debug.user_id}`);
                                        log(`  - آخر فحص: ${pollData.debug.last_check}`);
                                        log(`  - الوقت الحالي: ${pollData.debug.current_time}`);
                                        
                                        if (pollData.debug.all_messages_for_user) {
                                            log(`📋 جميع رسائل المستخدم:`);
                                            pollData.debug.all_messages_for_user.forEach(msg => {
                                                log(`  - ID: ${msg.id}, الوقت: ${msg.sent_at}, النص: ${msg.message}`);
                                            });
                                        }
                                    }
                                }
                            } else {
                                log(`❌ فشل في فحص الاستقبال: ${pollData.error}`);
                            }
                        } catch (error) {
                            log(`❌ خطأ في فحص الاستقبال: ${error.message}`);
                        }
                    }, 2000);
                    
                } else {
                    log(`❌ فشل في إرسال الرسالة: ${sendData.error}`);
                }
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`);
            }
        }
        
        // تحميل تلقائي عند فتح الصفحة
        window.addEventListener('load', () => {
            log('🚀 بدء فحص المستخدمين');
            setTimeout(loadUsers, 1000);
        });
    </script>
</body>
</html>
