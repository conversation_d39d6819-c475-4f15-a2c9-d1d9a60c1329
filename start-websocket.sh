#!/bin/bash

echo "🚀 بدء تشغيل خادم WebSocket للرسائل الفورية..."
echo

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "❌ PHP غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت PHP أولاً"
    exit 1
fi

# التحقق من وجود Composer
if ! command -v composer &> /dev/null; then
    echo "❌ Composer غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Composer أولاً"
    exit 1
fi

# تثبيت المكتبات المطلوبة
echo "📦 تثبيت المكتبات المطلوبة..."
composer install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المكتبات"
    exit 1
fi

echo "✅ تم تثبيت المكتبات بنجاح"
echo

# تشغيل خادم WebSocket
echo "🌐 تشغيل خادم WebSocket على المنفذ 8080..."
echo "📡 يمكن للعملاء الاتصال عبر: ws://localhost:8080"
echo
echo "⚠️  لإيقاف الخادم، اضغط Ctrl+C"
echo

php websocket-server.php
