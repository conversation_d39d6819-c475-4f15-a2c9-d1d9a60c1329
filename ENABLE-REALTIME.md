# 🚀 تفعيل الرسائل الفورية على الموقع المباشر

## ⚡ التشغيل السريع

### 1. رفع الملفات للخادم
```bash
# رفع هذه الملفات لمجلد الموقع:
- websocket-production.php
- composer.json
- setup-websocket-production.sh
```

### 2. تشغيل سكريبت الإعداد
```bash
# على الخادم المباشر
chmod +x setup-websocket-production.sh
sudo ./setup-websocket-production.sh
```

### 3. تحديث إعدادات قاعدة البيانات
في ملف `websocket-production.php`:
```php
$host = '127.0.0.1'; // عنوان خادم قاعدة البيانات
$dbname = 'csdb'; // اسم قاعدة البيانات الفعلي
$username = 'csdbuser'; // مستخدم قاعدة البيانات الفعلي
$password = 'كلمة_المرور_الفعلية'; // كلمة المرور الفعلية
```

### 4. بدء التشغيل
```bash
./start-websocket-production.sh
```

### 5. التحقق من التشغيل
```bash
# فحص حالة الخدمة
sudo systemctl status websocket-csmanager

# مراقبة السجلات
sudo journalctl -u websocket-csmanager -f

# أو استخدم سكريبت المراقبة
./monitor-websocket.sh
```

## 🔧 الطريقة اليدوية السريعة

إذا كنت تريد تشغيل سريع بدون إعداد خدمة:

### 1. تثبيت المكتبات
```bash
composer install
```

### 2. تشغيل الخادم مباشرة
```bash
# تشغيل في الخلفية
nohup php websocket-production.php > websocket.log 2>&1 &

# أو تشغيل في screen
screen -S websocket
php websocket-production.php
# اضغط Ctrl+A ثم D للخروج
```

### 3. فحص التشغيل
```bash
# فحص العملية
ps aux | grep websocket

# فحص المنفذ
netstat -tlnp | grep :8080

# فحص السجلات
tail -f websocket.log
```

## ✅ علامات النجاح

### في سجلات الخادم:
```
🚀 خادم WebSocket للإنتاج يعمل على 0.0.0.0:8080
🌐 متاح على: wss://www.csmanager.online:8080
📡 في انتظار الاتصالات...
✅ تم الاتصال بقاعدة البيانات بنجاح (الإنتاج)
```

### في التطبيق:
- 🟢 "رسائل فورية" بدلاً من "رسائل تقليدية"
- في Console: "🔗 تم الاتصال بخادم WebSocket"
- عمل جميع الميزات الفورية

## 🚨 استكشاف الأخطاء

### مشكلة: "Connection refused"
```bash
# فحص أن الخادم يعمل
sudo systemctl status websocket-csmanager
ps aux | grep websocket

# إعادة تشغيل
sudo systemctl restart websocket-csmanager
```

### مشكلة: "Database connection failed"
```bash
# فحص الاتصال بقاعدة البيانات
mysql -h 127.0.0.1 -u csdbuser -p csdb

# تحديث إعدادات قاعدة البيانات في websocket-production.php
```

### مشكلة: "Port already in use"
```bash
# فحص ما يستخدم المنفذ
sudo netstat -tlnp | grep :8080

# إيقاف العملية المتضاربة
sudo kill -9 PID_NUMBER
```

### مشكلة: "Permission denied"
```bash
# إعطاء صلاحيات للملفات
sudo chown -R www-data:www-data .
sudo chmod +x *.sh
```

## 🔄 إدارة الخدمة

### أوامر مفيدة:
```bash
# بدء التشغيل
sudo systemctl start websocket-csmanager

# إيقاف التشغيل
sudo systemctl stop websocket-csmanager

# إعادة التشغيل
sudo systemctl restart websocket-csmanager

# تفعيل التشغيل التلقائي
sudo systemctl enable websocket-csmanager

# إلغاء التشغيل التلقائي
sudo systemctl disable websocket-csmanager

# مراقبة السجلات
sudo journalctl -u websocket-csmanager -f
```

## 📊 مراقبة الأداء

### فحص الاتصالات النشطة:
```bash
netstat -an | grep :8080 | grep ESTABLISHED | wc -l
```

### فحص استخدام الذاكرة:
```bash
ps aux | grep websocket | grep -v grep
```

### فحص السجلات:
```bash
tail -f /var/log/websocket/output.log
tail -f /var/log/websocket/error.log
```

## 🎯 النتيجة المتوقعة

بعد التشغيل الناجح:
1. ✅ الرسائل تُرسل وتُستقبل فوراً
2. ✅ حالة الاتصال تظهر في الوقت الفعلي
3. ✅ مؤشر "يكتب..." يعمل
4. ✅ الإشعارات الصوتية تعمل
5. ✅ إشعارات المتصفح تعمل

الموقع سيصبح **تطبيق رسائل فورية كامل** مثل WhatsApp Web! 🎉
