/**
 * Simple Real-time Messaging System
 * Direct HTTP polling - no WebSocket complexity
 */

class SimpleRealtime {
    constructor() {
        this.isActive = false;
        this.pollingInterval = null;
        this.lastPollTime = null;
        this.currentUser = null;
        this.onlineUsers = new Set();
        this.messageHandlers = [];
        this.statusHandlers = [];
        this.pollIntervalMs = 3000; // 3 seconds
        this.apiUrl = 'realtime-simple.php';
        
        console.log('🚀 Simple Realtime System initialized');
    }
    
    // Initialize the system
    async init(user) {
        if (!user || !user.id) {
            console.error('❌ User required for initialization');
            return false;
        }
        
        this.currentUser = user;
        console.log('👤 Initializing for user:', user.name, '(' + user.id + ')');
        
        try {
            const response = await fetch(`${this.apiUrl}?action=init&user_id=${user.id}`);
            const data = await response.json();
            
            if (data.success) {
                console.log('✅ System initialized successfully');
                this.updateOnlineUsers(data.online_users || []);
                this.lastPollTime = data.timestamp;
                this.start();
                this.notifyStatusChange('connected');
                return true;
            } else {
                console.error('❌ Initialization failed:', data.error);
                return false;
            }
        } catch (error) {
            console.error('❌ Initialization error:', error);
            return false;
        }
    }
    
    // Start polling
    start() {
        if (this.isActive) {
            console.log('ℹ️ System already active');
            return;
        }
        
        this.isActive = true;
        console.log('🔄 Starting real-time polling...');
        
        this.pollingInterval = setInterval(() => {
            this.poll();
        }, this.pollIntervalMs);
        
        // Initial poll
        this.poll();
    }
    
    // Stop polling
    stop() {
        if (!this.isActive) return;
        
        this.isActive = false;
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        console.log('⏹️ Real-time polling stopped');
        this.notifyStatusChange('disconnected');
    }
    
    // Poll for updates
    async poll() {
        if (!this.currentUser || !this.isActive) return;
        
        try {
            const lastCheck = this.lastPollTime || new Date(Date.now() - 30000).toISOString();
            const url = `${this.apiUrl}?action=poll&user_id=${this.currentUser.id}&last_check=${encodeURIComponent(lastCheck)}`;
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                // Handle new messages
                if (data.new_messages && data.new_messages.length > 0) {
                    console.log(`📬 ${data.new_messages.length} new messages received`);
                    data.new_messages.forEach(message => {
                        this.handleNewMessage(message);
                    });
                }
                
                // Update online users
                if (data.online_users) {
                    this.updateOnlineUsers(data.online_users);
                }
                
                this.lastPollTime = data.timestamp;
                this.notifyStatusChange('connected');
            } else {
                console.warn('⚠️ Poll failed:', data.error);
            }
        } catch (error) {
            console.warn('⚠️ Poll error:', error.message);
        }
    }
    
    // Send message
    async sendMessage(receiverId, content, attachments = []) {
        if (!this.currentUser) {
            console.error('❌ No user logged in');
            return false;
        }
        
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'send',
                    sender_id: this.currentUser.id,
                    receiver_id: receiverId,
                    content: content,
                    attachments: attachments
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log('✅ Message sent successfully:', data.message_id);
                return true;
            } else {
                console.error('❌ Send failed:', data.error);
                return false;
            }
        } catch (error) {
            console.error('❌ Send error:', error);
            return false;
        }
    }
    
    // Handle new message
    handleNewMessage(message) {
        const formattedMessage = {
            id: message.id,
            sender_id: message.sender_id,
            sender_name: message.sender_name,
            receiver_id: message.receiver_id,
            content: message.message,
            attachments: message.attachments ? JSON.parse(message.attachments) : [],
            timestamp: message.sent_at,
            read: false
        };
        
        // Notify all message handlers
        this.messageHandlers.forEach(handler => {
            try {
                handler(formattedMessage);
            } catch (error) {
                console.error('❌ Message handler error:', error);
            }
        });
    }
    
    // Update online users
    updateOnlineUsers(users) {
        this.onlineUsers.clear();
        users.forEach(user => {
            this.onlineUsers.add(user.id);
        });
        
        // Notify status handlers
        this.statusHandlers.forEach(handler => {
            try {
                handler(users);
            } catch (error) {
                console.error('❌ Status handler error:', error);
            }
        });
    }
    
    // Notify status change
    notifyStatusChange(status) {
        const event = {
            type: 'status_change',
            status: status,
            timestamp: new Date().toISOString(),
            online_users_count: this.onlineUsers.size
        };
        
        this.statusHandlers.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                console.error('❌ Status change handler error:', error);
            }
        });
    }
    
    // Add message handler
    onMessage(handler) {
        this.messageHandlers.push(handler);
    }
    
    // Add status handler
    onStatusChange(handler) {
        this.statusHandlers.push(handler);
    }
    
    // Get system status
    getStatus() {
        return {
            active: this.isActive,
            user: this.currentUser,
            online_users_count: this.onlineUsers.size,
            last_poll: this.lastPollTime,
            poll_interval: this.pollIntervalMs
        };
    }
    
    // Check if user is online
    isUserOnline(userId) {
        return this.onlineUsers.has(userId);
    }
}

// Create global instance
window.SimpleRealtime = SimpleRealtime;
window.simpleRealtime = new SimpleRealtime();

console.log('📡 Simple Realtime module loaded');
console.log('🔧 Usage: simpleRealtime.init(currentUser)');
