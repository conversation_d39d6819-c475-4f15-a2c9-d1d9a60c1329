<?php
/**
 * اختبار مباشر لـ API الـ Polling
 */

// تضمين ملف websocket-fallback.php للاختبار
echo "<h1>اختبار API الـ Polling</h1>";
echo "<pre>";

// اختبار 1: Heartbeat
echo "=== اختبار Heartbeat ===\n";
$url = "websocket-fallback.php?action=heartbeat&user_id=test_user";
echo "URL: $url\n";

$response = file_get_contents($url);
echo "Response: $response\n\n";

// اختبار 2: Poll
echo "=== اختبار Poll ===\n";
$url = "websocket-fallback.php?action=poll&user_id=test_user&last_check=" . urlencode(date('Y-m-d H:i:s', time() - 3600));
echo "URL: $url\n";

$response = file_get_contents($url);
echo "Response: $response\n\n";

// اختبار 3: Send Message
echo "=== اختبار Send Message ===\n";
$postData = json_encode([
    'action' => 'send',
    'sender_id' => 'test_user',
    'receiver_id' => 'test_receiver',
    'content' => 'رسالة اختبار',
    'attachments' => []
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

echo "POST Data: $postData\n";
$response = file_get_contents('websocket-fallback.php', false, $context);
echo "Response: $response\n\n";

echo "</pre>";

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";
echo "<pre>";

try {
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ اتصال قاعدة البيانات نجح\n";
    
    // فحص جدول users
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    echo "\n📋 أعمدة جدول users:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    // فحص جدول messages
    $stmt = $pdo->query("DESCRIBE messages");
    $columns = $stmt->fetchAll();
    echo "\n📋 أعمدة جدول messages:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    // عدد المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $result = $stmt->fetch();
    echo "\n👥 عدد المستخدمين النشطين: {$result['count']}\n";
    
    // عدد الرسائل
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM messages");
    $result = $stmt->fetch();
    echo "📨 عدد الرسائل: {$result['count']}\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
